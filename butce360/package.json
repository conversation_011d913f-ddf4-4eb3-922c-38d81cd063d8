{"name": "butce360", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.4.1", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.4", "@react-native-google-signin/google-signin": "^15.0.0", "@react-native/new-app-screen": "0.81.0", "axios": "^1.11.0", "react": "19.1.0", "react-native": "0.81.0", "react-native-blob-util": "^0.22.2", "react-native-document-picker": "^9.3.1", "react-native-safe-area-context": "^5.5.2", "react-native-svg": "^15.12.1", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "^20.0.1", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.0", "@react-native/eslint-config": "0.81.0", "@react-native/metro-config": "0.81.0", "@react-native/typescript-config": "0.81.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "^5.5.4"}, "engines": {"node": ">=18"}}