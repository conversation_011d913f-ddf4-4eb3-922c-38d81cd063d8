/**
 * Butce360 - Personal Finance Management App
 * Pure React Native Implementation
 */

import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { ThemeProvider } from './src/context/ThemeContext';
import { AuthProvider, useAuth } from './src/context/AuthContext';
import MainNavigator from './src/navigation/MainNavigator';
import { useThemedColors } from './src/hooks/useThemedStyles';
import { GoogleSignin } from '@react-native-google-signin/google-signin';

// Loading screen component
const LoadingScreen: React.FC = () => {
  const colors = useThemedColors();

  return (
    <View style={[loadingStyles.container, { backgroundColor: colors.background.primary }]}>
      <ActivityIndicator size="large" color={colors.primary[500]} />
      <Text style={[loadingStyles.text, { color: colors.text.secondary }]}>
        Yükleniyor...
      </Text>
    </View>
  );
};

// App content component - direct to main app (no auth required)
const AppContent: React.FC = () => {
  console.log('[App] Starting app directly to Home screen');

  return <MainNavigator />;
};

function App() {
  useEffect(() => {
    // Configure Google Sign-In
    GoogleSignin.configure({
      webClientId: '102312339260-ns38t9cipmomg5v8laqk7ii67ud28ona.apps.googleusercontent.com', // Google Cloud Console Web Client ID
      iosClientId: '102312339260-ns38t9cipmomg5v8laqk7ii67ud28ona.apps.googleusercontent.com', // Google Cloud Console iOS Client ID
      offlineAccess: true,
      hostedDomain: '',
      forceCodeForRefreshToken: true,
    });

    console.log('App initialized with social login');
  }, []);

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}

// Loading screen styles
const loadingStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
  },
});

export default App;
