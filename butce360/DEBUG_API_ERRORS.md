# 🐛 API Error Debug Guide

## ✅ **Düzeltilen Sorunlar:**

### 1. **TypeError: Cannot read property 'length' of undefined**
- **Sorun:** `transactions` array'i undefined olabiliyordu
- **Çözüm:** Null check'ler eklendi
```typescript
// Önceki
transactions.length > 0

// Sonraki  
transactions && transactions.length > 0
```

### 2. **Service Error Handling İyileştirildi:**
- **TransactionService:** Empty array return ediyor error durumunda
- **ReportService:** Null return ediyor error durumunda
- **HomeScreen:** Individual error handling eklendi

## 🔍 **Debug Adımları:**

### 1. **API Response'ları Kontrol Edin:**
```bash
# iOS Simulator'da Console açın
# Xcode -> Window -> Devices and Simulators -> Simulator -> Console
```

### 2. **Network Request'leri İzleyin:**
```typescript
// API client'ta debug log'lar aktif
debugLog('API Request:', { url, method, headers, body });
debugLog('API Response:', { url, status, data });
```

### 3. **Backend API Test:**
```bash
# Backend'inizin çalıştığını kontrol edin
curl -X GET "http://localhost:8008/api/v1/transactions" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Reports endpoint test
curl -X GET "http://localhost:8008/api/v1/reports/summary" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🚨 **Olası Sorunlar ve Çözümleri:**

### 1. **Backend Çalışmıyor:**
```bash
# Backend'i başlatın
cd backend
go run main.go
```

### 2. **CORS Sorunu:**
```go
// Backend'de CORS middleware ekleyin
r.Use(cors.New(cors.Config{
    AllowOrigins:     []string{"*"},
    AllowMethods:     []string{"GET", "POST", "PUT", "DELETE"},
    AllowHeaders:     []string{"*"},
    AllowCredentials: true,
}))
```

### 3. **Token Sorunu:**
```typescript
// AuthService'te token kontrol edin
const token = await authService.getStoredToken();
console.log('Current token:', token);
```

### 4. **API Response Format Sorunu:**
Backend'inizin response format'ı:
```json
{
  "data": [...],
  "message": "Success",
  "status": 200
}
```

Veya direkt data:
```json
[...]
```

## 🔧 **Test Komutları:**

### **iOS Console Log'ları:**
```bash
# Terminal'de iOS simulator log'larını izleyin
xcrun simctl spawn booted log stream --predicate 'process == "butce360"'
```

### **Network Debug:**
```typescript
// API client'ta debug mode aktif
const API_CONFIG = {
  DEBUG: __DEV__, // true olmalı development'ta
};
```

## 📱 **Uygulama Test Adımları:**

1. **Login yapın**
2. **HomeScreen açılsın**
3. **Console'da log'ları kontrol edin:**
   ```
   [TransactionService] Fetched recent transactions: [...]
   [ReportService] Fetched summary: {...}
   ```

4. **Error varsa:**
   ```
   [TransactionService] Error fetching recent transactions: {...}
   [ReportService] Error fetching summary: {...}
   ```

## 🎯 **Sonraki Adımlar:**

1. **Backend API'yi test edin** - Postman veya curl ile
2. **Token'ın geçerli olduğunu kontrol edin**
3. **Network connectivity kontrol edin**
4. **CORS ayarlarını kontrol edin**

## 🚀 **Başarılı Test Sonrası:**

Eğer API'ler düzgün çalışıyorsa:
- HomeScreen'de gerçek data görünmeli
- TransactionsScreen'de transaction listesi olmalı
- Pull-to-refresh çalışmalı
- Error handling düzgün çalışmalı

**Hangi adımda sorun yaşıyorsunuz?**
