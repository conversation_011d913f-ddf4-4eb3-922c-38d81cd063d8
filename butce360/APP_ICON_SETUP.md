# App Icon Setup Guide

Bu rehber, Butce360Logo.png dosyasını kullanarak iOS ve Android app icon'larını nasıl oluşturacağınızı açıklar.

## Gerekli Icon Boyutları

### iOS (butce360/ios/butce360/Images.xcassets/AppIcon.appiconset/)

Aşağıdaki boyutlarda icon'lar oluştur<PERSON> ve ilgili klasöre koyun:

- **<EMAIL>** - 40x40 px
- **<EMAIL>** - 60x60 px
- **<EMAIL>** - 58x58 px
- **<EMAIL>** - 87x87 px
- **<EMAIL>** - 80x80 px
- **<EMAIL>** - 120x120 px
- **<EMAIL>** - 120x120 px
- **<EMAIL>** - 180x180 px
- **1024x1024.png** - 1024x1024 px (App Store)

### Android (butce360/android/app/src/main/res/)

Aşağıdaki klasörlere uygun boyutlarda icon'lar koyun:

- **mipmap-mdpi/ic_launcher.png** - 48x48 px
- **mipmap-hdpi/ic_launcher.png** - 72x72 px
- **mipmap-xhdpi/ic_launcher.png** - 96x96 px
- **mipmap-xxhdpi/ic_launcher.png** - 144x144 px
- **mipmap-xxxhdpi/ic_launcher.png** - 192x192 px

Round icon'lar için de aynı boyutlarda:
- **mipmap-mdpi/ic_launcher_round.png** - 48x48 px
- **mipmap-hdpi/ic_launcher_round.png** - 72x72 px
- **mipmap-xhdpi/ic_launcher_round.png** - 96x96 px
- **mipmap-xxhdpi/ic_launcher_round.png** - 144x144 px
- **mipmap-xxxhdpi/ic_launcher_round.png** - 192x192 px

## Icon Oluşturma Araçları

### Online Araçlar:
1. **App Icon Generator** - https://appicon.co/
2. **Icon Kitchen** - https://icon.kitchen/
3. **MakeAppIcon** - https://makeappicon.com/

### Yerel Araçlar:
1. **ImageMagick** (Command line)
2. **Photoshop** veya **GIMP**
3. **Sketch** (Mac)

## ImageMagick ile Otomatik Oluşturma

Eğer ImageMagick kuruluysa, aşağıdaki komutları kullanabilirsiniz:

```bash
# iOS icon'ları oluştur
convert src/assets/images/Butce360Logo.png -resize 40x40 ios/butce360/Images.xcassets/AppIcon.appiconset/<EMAIL>
convert src/assets/images/Butce360Logo.png -resize 60x60 ios/butce360/Images.xcassets/AppIcon.appiconset/<EMAIL>
convert src/assets/images/Butce360Logo.png -resize 58x58 ios/butce360/Images.xcassets/AppIcon.appiconset/<EMAIL>
convert src/assets/images/Butce360Logo.png -resize 87x87 ios/butce360/Images.xcassets/AppIcon.appiconset/<EMAIL>
convert src/assets/images/Butce360Logo.png -resize 80x80 ios/butce360/Images.xcassets/AppIcon.appiconset/<EMAIL>
convert src/assets/images/Butce360Logo.png -resize 120x120 ios/butce360/Images.xcassets/AppIcon.appiconset/<EMAIL>
convert src/assets/images/Butce360Logo.png -resize 120x120 ios/butce360/Images.xcassets/AppIcon.appiconset/<EMAIL>
convert src/assets/images/Butce360Logo.png -resize 180x180 ios/butce360/Images.xcassets/AppIcon.appiconset/<EMAIL>
convert src/assets/images/Butce360Logo.png -resize 1024x1024 ios/butce360/Images.xcassets/AppIcon.appiconset/1024x1024.png

# Android icon'ları oluştur
convert src/assets/images/Butce360Logo.png -resize 48x48 android/app/src/main/res/mipmap-mdpi/ic_launcher.png
convert src/assets/images/Butce360Logo.png -resize 72x72 android/app/src/main/res/mipmap-hdpi/ic_launcher.png
convert src/assets/images/Butce360Logo.png -resize 96x96 android/app/src/main/res/mipmap-xhdpi/ic_launcher.png
convert src/assets/images/Butce360Logo.png -resize 144x144 android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png
convert src/assets/images/Butce360Logo.png -resize 192x192 android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png

# Android round icon'ları oluştur (yuvarlak köşeler için)
convert src/assets/images/Butce360Logo.png -resize 48x48 android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png
convert src/assets/images/Butce360Logo.png -resize 72x72 android/app/src/main/res/mipmap-hdpi/ic_launcher_round.png
convert src/assets/images/Butce360Logo.png -resize 96x96 android/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png
convert src/assets/images/Butce360Logo.png -resize 144x144 android/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png
convert src/assets/images/Butce360Logo.png -resize 192x192 android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png
```

## Önemli Notlar

1. **iOS icon'ları** köşeleri otomatik olarak yuvarlatılır, bu yüzden kare olarak bırakabilirsiniz.
2. **Android round icon'ları** için logoyu yuvarlak bir maskeye uygun şekilde düzenleyebilirsiniz.
3. Icon'lar **şeffaf arka plan** olmamalı (iOS için).
4. **1024x1024** boyutundaki icon App Store için gereklidir.
5. Icon'ları değiştirdikten sonra projeyi **clean build** yapın.

## Build Sonrası

Icon'ları değiştirdikten sonra:

```bash
# iOS için
cd ios && rm -rf build && cd ..
npx react-native run-ios

# Android için
cd android && ./gradlew clean && cd ..
npx react-native run-android
```

## Logo Bileşeni Kullanımı

Uygulama içinde logo göstermek için:

```tsx
import Logo from '../components/common/Logo';

// Kare köşeli
<Logo size={60} variant="square" />

// Yuvarlak köşeli
<Logo size={60} variant="rounded" />

// Tamamen yuvarlak
<Logo size={60} variant="circle" />
```
