import React from 'react';
import {
  View,
  Image,
  // StyleSheet, // Not used
  ViewStyle,
  ImageStyle,
} from 'react-native';
import { spacing } from '../../theme/spacing';

interface LogoProps {
  size?: number;
  variant?: 'square' | 'rounded' | 'circle';
  style?: ViewStyle;
  imageStyle?: ImageStyle;
}

const Logo: React.FC<LogoProps> = ({
  size = 60,
  variant = 'rounded',
  style,
  imageStyle,
}) => {
  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      width: size,
      height: size,
      overflow: 'hidden',
    };

    switch (variant) {
      case 'square':
        baseStyle.borderRadius = 0;
        break;
      case 'circle':
        baseStyle.borderRadius = size / 2;
        break;
      case 'rounded':
      default:
        baseStyle.borderRadius = spacing.cardRadius;
        break;
    }

    return baseStyle;
  };

  const getImageStyle = (): ImageStyle => {
    return {
      width: size,
      height: size,
      resizeMode: 'contain',
    };
  };

  return (
    <View style={[getContainerStyle(), style]}>
      <Image
        source={require('../../assets/images/Butce360Logo.png')}
        style={[getImageStyle(), imageStyle]}
      />
    </View>
  );
};

export default Logo;
