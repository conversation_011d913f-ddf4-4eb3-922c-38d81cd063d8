import React from 'react';
import {
  View,
  // StyleSheet, // Not used
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../../theme/colors';
import { spacing } from '../../theme/spacing';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: number;
  margin?: number;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined';
  disabled?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  style,
  padding = spacing.cardPadding,
  margin = 0,
  onPress,
  variant = 'default',
  disabled = false,
}) => {
  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: colors.surface.primary,
      borderRadius: spacing.cardRadius,
      padding,
      margin,
    };

    // Variant styles
    switch (variant) {
      case 'elevated':
        baseStyle.shadowColor = colors.neutral[900];
        baseStyle.shadowOffset = { width: 0, height: 2 };
        baseStyle.shadowOpacity = 0.1;
        baseStyle.shadowRadius = 4;
        baseStyle.elevation = 3;
        break;
      case 'outlined':
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = colors.border.primary;
        break;
      default:
        baseStyle.shadowColor = colors.neutral[900];
        baseStyle.shadowOffset = { width: 0, height: 1 };
        baseStyle.shadowOpacity = 0.05;
        baseStyle.shadowRadius = 2;
        baseStyle.elevation = 1;
    }

    // Disabled state
    if (disabled) {
      baseStyle.opacity = 0.6;
    }

    return baseStyle;
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={[getCardStyle(), style]}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.7}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );
};

export default Card;
