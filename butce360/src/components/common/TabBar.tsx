import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '../../navigation/AppNavigator';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

interface TabItem {
  key: string;
  title: string;
  icon: string;
  screen: string;
}

const tabs: TabItem[] = [
  { key: 'home', title: 'Ana Sayfa', icon: 'home-outline', screen: 'Home' },
  { key: 'transactions', title: '<PERSON><PERSON><PERSON><PERSON>', icon: 'card-outline', screen: 'Transactions' },
  { key: 'budget', title: 'Bütçe', icon: 'target-outline', screen: 'Budget' },
  { key: 'reports', title: 'Raporlar', icon: 'bar-chart-outline', screen: 'Reports' },
  { key: 'profile', title: 'Profil', icon: 'person-outline', screen: 'Profile' },
];

interface TabBarProps {
  activeTab: string;
}

const TabBar: React.FC<TabBarProps> = ({ activeTab }) => {
  const { navigate } = useNavigation();

  const handleTabPress = (tab: TabItem) => {
    navigate(tab.screen);
  };

  return (
    <View style={styles.container}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.key;
        
        return (
          <TouchableOpacity
            key={tab.key}
            style={[styles.tab, isActive && styles.activeTab]}
            onPress={() => handleTabPress(tab)}
          >
            <Ionicons
              name={tab.icon}
              size={24}
              color={isActive ? colors.primary[500] : colors.text.secondary}
            />
            <Text style={[styles.title, isActive && styles.activeTitle]}>
              {tab.title}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: colors.surface.primary,
    borderTopWidth: 1,
    borderTopColor: colors.border.primary,
    paddingBottom: spacing.md,
    paddingTop: spacing.sm,
    paddingHorizontal: spacing.xs,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
    borderRadius: spacing.cardRadius,
  },
  activeTab: {
    backgroundColor: colors.primary[50],
  },
  icon: {
    fontSize: 20,
    marginBottom: spacing.xs,
  },
  activeIcon: {
    // Could add different styling for active icon
  },
  title: {
    ...typography.styles.tabLabel,
    color: colors.text.secondary,
  },
  activeTitle: {
    color: colors.primary[600],
    fontWeight: '600',
  },
});

export default TabBar;
