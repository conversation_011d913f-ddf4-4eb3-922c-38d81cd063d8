import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';

interface NavigationHeaderProps {
  title: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightComponent?: React.ReactNode;
  backgroundColor?: string;
}

const NavigationHeader: React.FC<NavigationHeaderProps> = ({
  title,
  showBackButton = true,
  onBackPress,
  rightComponent,
  backgroundColor,
}) => {
  const colors = useThemedColors();
  const insets = useSafeAreaInsets();

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: insets.top + 8,
          backgroundColor: backgroundColor || colors.background.primary,
          borderBottomColor: colors.border.primary,
        },
      ]}
    >
      <View style={styles.content}>
        {showBackButton && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={onBackPress}
            activeOpacity={0.7}
          >
            <Text style={[styles.backIcon, { color: colors.primary[500] }]}>
              ‹
            </Text>
            <Text style={[styles.backText, { color: colors.primary[500] }]}>
              Geri
            </Text>
          </TouchableOpacity>
        )}
        
        <View style={styles.titleContainer}>
          <Text
            style={[
              styles.title,
              { color: colors.text.primary },
              !showBackButton && styles.titleCentered,
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {title}
          </Text>
        </View>

        <View style={styles.rightContainer}>
          {rightComponent}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 0.5,
    paddingBottom: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    minHeight: 44,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingRight: 8,
    marginLeft: -4,
  },
  backIcon: {
    fontSize: 24,
    fontWeight: '400',
    marginRight: 4,
  },
  backText: {
    ...typography.styles.body,
    fontWeight: '400',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  title: {
    ...typography.styles.navigationTitle,
    textAlign: 'center',
  },
  titleCentered: {
    textAlign: 'center',
  },
  rightContainer: {
    minWidth: 60,
    alignItems: 'flex-end',
  },
});

export default NavigationHeader;
