import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';

interface BackButtonProps {
  onPress: () => void;
  title?: string;
  showTitle?: boolean;
}

const BackButton: React.FC<BackButtonProps> = ({ 
  onPress, 
  title = 'Geri',
  showTitle = true 
}) => {
  const colors = useThemedColors();
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <TouchableOpacity
        style={styles.button}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <Text style={[styles.backIcon, { color: colors.primary[500] }]}>
          ‹
        </Text>
        {showTitle && (
          <Text style={[styles.title, { color: colors.primary[500] }]}>
            {title}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  backIcon: {
    fontSize: 28,
    fontWeight: '300',
    marginRight: 4,
  },
  title: {
    ...typography.styles.body,
    fontWeight: '400',
  },
});

export default BackButton;
