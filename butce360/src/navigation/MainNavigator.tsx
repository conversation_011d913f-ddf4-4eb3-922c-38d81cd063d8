import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useThemedColors } from '../hooks/useThemedStyles';

// Import screens
import HomeScreen from '../screens/Home/HomeScreen';
import AddTransactionScreen from '../screens/Transactions/AddTransactionScreen';
import MenuScreen from '../screens/Menu/MenuScreen';
import BudgetScreen from '../screens/Budget/BudgetScreen';
import AddBudgetScreen from '../screens/Budget/AddBudgetScreen';
import CategoriesScreen from '../screens/Categories/CategoriesScreen';
import AccountsScreen from '../screens/Accounts/AccountsScreen';
import ReportsScreen from '../screens/Reports/ReportsScreen';
import SettingsScreen from '../screens/Settings/SettingsScreen';
import ThemeScreen from '../screens/Theme/ThemeScreen';
import ProfileScreen from '../screens/Profile/ProfileScreen';
import EditProfileScreen from '../screens/Profile/EditProfileScreen';
import ChangePasswordScreen from '../screens/Profile/ChangePasswordScreen';
import AboutScreen from '../screens/About/AboutScreen';
import HelpScreen from '../screens/Help/HelpScreen';
import TransactionsScreen from '../screens/Transactions/TransactionsScreen';
import LoginScreen from '../screens/Auth/LoginScreen';
import RegisterScreen from '../screens/Auth/RegisterScreen';
import BankStatementScreen from '../screens/BankStatement/BankStatementScreen';
import ExportScreen from '../screens/Export/ExportScreen';

// Import components
import TabBar from './BottomTabNavigator';
import NavigationHeader from '../components/common/NavigationHeader';

export interface NavigationProps {
  onNavigate: (screen: string) => void;
  onGoBack?: () => void;
}

const MainNavigator: React.FC = () => {
  const [activeTab, setActiveTab] = useState('home');
  const [currentScreen, setCurrentScreen] = useState('home');
  const [navigationHistory, setNavigationHistory] = useState<string[]>(['home']);
  const colors = useThemedColors();

  const handleNavigate = (screen: string) => {
    console.log('[MainNavigator] Navigating to screen:', screen);
    setCurrentScreen(screen);
    setNavigationHistory(prev => [...prev, screen]);

    // Ana tab sayfalarına dönüldüğünde activeTab'ı da güncelle
    const normalizedScreen = screen.toLowerCase();
    if (normalizedScreen === 'home') {
      setActiveTab('home');
      setCurrentScreen('home');
    } else if (normalizedScreen === 'menu') {
      setActiveTab('menu');
      setCurrentScreen('menu');
    }
  };

  const handleGoBack = () => {
    console.log('[MainNavigator] Going back');
    if (navigationHistory.length > 1) {
      const newHistory = [...navigationHistory];
      newHistory.pop(); // Remove current screen
      const previousScreen = newHistory[newHistory.length - 1];

      setNavigationHistory(newHistory);
      setCurrentScreen(previousScreen);

      // If going back to a tab screen, update active tab
      if (['home', 'add', 'menu'].includes(previousScreen)) {
        setActiveTab(previousScreen);
      }
    } else {
      // If no history, go to menu
      setCurrentScreen('menu');
      setActiveTab('menu');
      setNavigationHistory(['menu']);
    }
  };

  const handleTabPress = (tab: string) => {
    console.log('[MainNavigator] Tab pressed:', tab);
    setActiveTab(tab);
    setCurrentScreen(tab);
    setNavigationHistory([tab]); // Reset history when tab is pressed
  };

  const getScreenTitle = (screen: string): string => {
    const titles: { [key: string]: string } = {
      home: 'Ana Sayfa',
      add: 'İşlem Ekle',
      menu: 'Menü',
      budget: 'Bütçe',
      categories: 'Kategoriler',
      accounts: 'Hesaplar',
      reports: 'Raporlar',
      settings: 'Ayarlar',
      theme: 'Tema',
      profile: 'Profil',
      about: 'Hakkında',
      help: 'Yardım',
      transactions: 'İşlemler',
    };
    return titles[screen] || screen;
  };

  const isTabScreen = (screen: string): boolean => {
    return ['home', 'add', 'menu'].includes(screen);
  };

  const renderScreenWithHeader = (screenComponent: React.ReactNode, showHeader: boolean = true) => {
    if (!showHeader || isTabScreen(currentScreen)) {
      return screenComponent;
    }

    return (
      <>
        <NavigationHeader
          title={getScreenTitle(currentScreen)}
          showBackButton={navigationHistory.length > 1}
          onBackPress={handleGoBack}
        />
        {screenComponent}
      </>
    );
  };

  const renderScreen = () => {
    // Handle non-tab screens first
    switch (currentScreen) {
      case 'budget':
        return renderScreenWithHeader(<BudgetScreen onNavigate={handleNavigate} />);
      case 'categories':
        return renderScreenWithHeader(<CategoriesScreen />);
      case 'accounts':
        return renderScreenWithHeader(<AccountsScreen onNavigate={handleNavigate} />);
      case 'reports':
        return renderScreenWithHeader(<ReportsScreen />);
      case 'settings':
        return renderScreenWithHeader(<SettingsScreen onNavigate={handleNavigate} />);
      case 'theme':
        return renderScreenWithHeader(<ThemeScreen onNavigate={handleNavigate} />);
      case 'profile':
        return renderScreenWithHeader(<ProfileScreen onNavigate={handleNavigate} />);
      case 'about':
        return renderScreenWithHeader(<AboutScreen />);
      case 'help':
        return renderScreenWithHeader(<HelpScreen />);
      case 'transactions':
        return renderScreenWithHeader(<TransactionsScreen />);
      case 'login':
        return <LoginScreen onNavigate={handleNavigate} />;
      case 'register':
        return <RegisterScreen onNavigate={handleNavigate} />;
      case 'bankStatement':
        return <BankStatementScreen onNavigate={handleNavigate} />;
      case 'export':
        return <ExportScreen onNavigate={handleNavigate} />;
      case 'addBudget':
        return <AddBudgetScreen onNavigate={handleNavigate} />;
      case 'editProfile':
        return renderScreenWithHeader(<EditProfileScreen />);
      case 'changePassword':
        return renderScreenWithHeader(<ChangePasswordScreen />);
      // Add more screens as needed
    }

    // Handle tab screens
    switch (activeTab) {
      case 'home':
        return <HomeScreen />;
      case 'add':
        return <AddTransactionScreen />;
      case 'menu':
        return <MenuScreen onNavigate={handleNavigate} />;
      default:
        return <HomeScreen />;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.screenContainer}>
        {renderScreen()}
      </View>
      {isTabScreen(currentScreen) && (
        <TabBar activeTab={activeTab} onTabPress={handleTabPress} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
  },
});

export default MainNavigator;
