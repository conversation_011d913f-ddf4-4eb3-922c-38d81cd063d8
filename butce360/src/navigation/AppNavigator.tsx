import React, { useState, useContext, createContext } from 'react';
import { View, StyleSheet } from 'react-native';

// Navigation context for managing app navigation state
interface NavigationState {
  currentScreen: string;
  params?: any;
  history: Array<{ screen: string; params?: any }>;
}

interface NavigationContextType {
  navigationState: NavigationState;
  navigate: (screen: string, params?: any) => void;
  goBack: () => void;
  reset: (screen: string, params?: any) => void;
  canGoBack: () => boolean;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within NavigationProvider');
  }
  return context;
};

interface NavigationProviderProps {
  children: React.ReactNode;
  initialScreen?: string;
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({
  children,
  initialScreen = 'Welcome'
}) => {
  const [navigationState, setNavigationState] = useState<NavigationState>({
    currentScreen: initialScreen,
    params: undefined,
    history: [{ screen: initialScreen }],
  });

  const navigate = (screen: string, params?: any) => {
    setNavigationState(prev => ({
      currentScreen: screen,
      params,
      history: [...prev.history, { screen, params }],
    }));
  };

  const goBack = () => {
    setNavigationState(prev => {
      if (prev.history.length <= 1) return prev;

      const newHistory = [...prev.history];
      newHistory.pop(); // Remove current screen
      const previousScreen = newHistory[newHistory.length - 1];

      return {
        currentScreen: previousScreen.screen,
        params: previousScreen.params,
        history: newHistory,
      };
    });
  };

  const reset = (screen: string, params?: any) => {
    setNavigationState({
      currentScreen: screen,
      params,
      history: [{ screen, params }],
    });
  };

  const canGoBack = () => {
    return navigationState.history.length > 1;
  };

  const contextValue: NavigationContextType = {
    navigationState,
    navigate,
    goBack,
    reset,
    canGoBack,
  };

  return (
    <NavigationContext.Provider value={contextValue}>
      {children}
    </NavigationContext.Provider>
  );
};

// Screen wrapper component
interface ScreenProps {
  name: string;
  component: React.ComponentType<any>;
  currentScreen: string;
  params?: any;
}

const Screen: React.FC<ScreenProps> = ({ name, component: Component, currentScreen, params }) => {
  if (name !== currentScreen) {
    return null;
  }

  return (
    <View style={styles.screen}>
      <Component route={{ params }} />
    </View>
  );
};

// Main App Navigator component
interface AppNavigatorProps {
  children?: React.ReactNode;
}

export const AppNavigator: React.FC<AppNavigatorProps> = ({ children }) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { navigationState } = useNavigation();
  // navigationState is used in cloneElement calls below (lines 148-149)

  return (
    <View style={styles.container}>
      {children}
    </View>
  );
};

// Stack Navigator component
interface StackNavigatorProps {
  initialRouteName: string;
  children: React.ReactNode;
}

export const StackNavigator: React.FC<StackNavigatorProps> = ({
  initialRouteName: _initialRouteName,
  children
}) => {
  const { navigationState } = useNavigation();

  return (
    <View style={styles.container}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child as React.ReactElement, {
            currentScreen: navigationState.currentScreen,
            params: navigationState.params,
          } as any);
        }
        return child;
      })}
    </View>
  );
};

// Tab Navigator component
interface TabNavigatorProps {
  initialRouteName: string;
  children: React.ReactNode;
}

export const TabNavigator: React.FC<TabNavigatorProps> = ({
  initialRouteName: _initialRouteName,
  children
}) => {
  const { navigationState } = useNavigation();

  return (
    <View style={styles.container}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child as React.ReactElement, {
            currentScreen: navigationState.currentScreen,
            params: navigationState.params,
          } as any);
        }
        return child;
      })}
    </View>
  );
};

// Screen component for use in navigators
export { Screen };

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  screen: {
    flex: 1,
  },
});