// Navigation types for React Navigation

export type RootStackParamList = {
  // Auth Stack
  Welcome: undefined;
  Login: undefined;
  Register: undefined;

  // Main App
  MainTabs: undefined;

  // Modal screens
  AddTransaction: {
    type?: 'income' | 'expense';
    transactionId?: string;
  };
  EditTransaction: {
    transactionId: string;
  };
  AddCategory: {
    type?: 'income' | 'expense';
  };
  EditCategory: {
    categoryId: string;
  };
  AddAccount: undefined;
  EditAccount: {
    accountId: string;
  };
  TransactionDetails: {
    transactionId: string;
  };
  CategoryDetails: {
    categoryId: string;
  };
  AccountDetails: {
    accountId: string;
  };
};

export type MainTabParamList = {
  Home: undefined;
  Transactions: undefined;
  Budget: undefined;
  Reports: undefined;
  Profile: undefined;
};

export type HomeStackParamList = {
  HomeScreen: undefined;
  TransactionList: {
    filter?: {
      type?: 'income' | 'expense';
      categoryId?: string;
      accountId?: string;
      startDate?: Date;
      endDate?: Date;
    };
  };
};

export type TransactionStackParamList = {
  TransactionList: undefined;
  TransactionDetails: {
    transactionId: string;
  };
};

export type BudgetStackParamList = {
  BudgetList: undefined;
  BudgetDetails: {
    budgetId: string;
  };
  CreateBudget: undefined;
  EditBudget: {
    budgetId: string;
  };
};

export type ReportStackParamList = {
  ReportOverview: undefined;
  CategoryReport: {
    categoryId?: string;
    type?: 'income' | 'expense';
  };
  MonthlyReport: {
    year: number;
    month: number;
  };
  YearlyReport: {
    year: number;
  };
};

export type ProfileStackParamList = {
  ProfileScreen: undefined;
  Settings: undefined;
  Categories: undefined;
  Accounts: undefined;
  EditProfile: undefined;
  ChangePassword: undefined;
  About: undefined;
  Help: undefined;
};

// Screen props types
export type ScreenProps<T extends keyof RootStackParamList> = {
  route: {
    params: RootStackParamList[T];
  };
  navigation: any; // Will be properly typed with navigation prop
};

export type TabScreenProps<T extends keyof MainTabParamList> = {
  route: {
    params: MainTabParamList[T];
  };
  navigation: any;
};