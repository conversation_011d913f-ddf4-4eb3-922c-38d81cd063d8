import { Category } from '../types/models';

export const DEFAULT_CATEGORIES: Category[] = [
  // <PERSON><PERSON><PERSON>
  {
    id: 'income-salary',
    name: '<PERSON><PERSON><PERSON>',
    type: 'income',
    icon: 'briefcase-outline',
    color: '#10B981',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-freelance',
    name: 'Serbest Çalışma',
    type: 'income',
    icon: 'laptop-outline',
    color: '#059669',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-investment',
    name: 'Ya<PERSON><PERSON><PERSON><PERSON><PERSON>',
    type: 'income',
    icon: 'trending-up-outline',
    color: '#047857',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-bonus',
    name: 'Bonus',
    type: 'income',
    icon: 'gift-outline',
    color: '#065F46',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-other',
    name: '<PERSON><PERSON><PERSON>',
    type: 'income',
    icon: 'add-circle-outline',
    color: '#064E3B',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },

  // Gider Kategorileri
  {
    id: 'expense-food',
    name: 'Yemek',
    type: 'expense',
    icon: 'restaurant-outline',
    color: '#EF4444',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-transport',
    name: 'Ulaşım',
    type: 'expense',
    icon: 'car-outline',
    color: '#DC2626',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-shopping',
    name: 'Alışveriş',
    type: 'expense',
    icon: 'bag-outline',
    color: '#B91C1C',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-bills',
    name: 'Faturalar',
    type: 'expense',
    icon: 'receipt-outline',
    color: '#991B1B',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-entertainment',
    name: 'Eğlence',
    type: 'expense',
    icon: 'game-controller-outline',
    color: '#7F1D1D',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-health',
    name: 'Sağlık',
    type: 'expense',
    icon: 'medical-outline',
    color: '#F97316',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-education',
    name: 'Eğitim',
    type: 'expense',
    icon: 'school-outline',
    color: '#EA580C',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-home',
    name: 'Ev',
    type: 'expense',
    icon: 'home-outline',
    color: '#C2410C',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-clothing',
    name: 'Giyim',
    type: 'expense',
    icon: 'shirt-outline',
    color: '#9A3412',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-other',
    name: 'Diğer Gider',
    type: 'expense',
    icon: 'remove-circle-outline',
    color: '#7C2D12',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export const getDefaultCategoriesByType = (type: 'income' | 'expense'): Category[] => {
  return DEFAULT_CATEGORIES.filter(category => category.type === type);
};

export const getDefaultCategoryById = (id: string): Category | undefined => {
  return DEFAULT_CATEGORIES.find(category => category.id === id);
};
