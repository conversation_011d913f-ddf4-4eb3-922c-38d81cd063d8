// Modern color palette for Butce360 app

export const colors = {
  // Primary colors
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9', // Main primary
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },

  // Secondary colors (Green for income)
  secondary: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // Main secondary
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },

  // Accent colors (Orange for expenses)
  accent: {
    50: '#fff7ed',
    100: '#ffedd5',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#f97316', // Main accent
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12',
  },

  // Neutral colors
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
  },

  // Semantic colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },

  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },

  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },

  info: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },

  // Background colors
  background: {
    primary: '#f2f2f7',    // New light theme background
    secondary: '#ffffff',   // White for cards/surfaces
    tertiary: '#f8fafc',   // Light gray for subtle backgrounds
  },

  // Surface colors
  surface: {
    primary: '#ffffff',
    secondary: '#f8fafc',
    elevated: '#ffffff',
  },

  // Text colors
  text: {
    primary: '#1e293b',
    secondary: '#64748b',
    tertiary: '#94a3b8',
    inverse: '#ffffff',
    disabled: '#cbd5e1',
  },

  // Additional text colors for compatibility
  textSecondary: '#64748b',

  // Border colors
  border: {
    primary: '#e2e8f0',
    secondary: '#cbd5e1',
    focus: '#0ea5e9',
  },

  // Shadow colors
  shadow: '#000000',

  // Error background
  errorBackground: '#fef2f2',

  // Category colors for transactions
  categories: {
    food: '#ff6b6b',
    transport: '#4ecdc4',
    shopping: '#45b7d1',
    entertainment: '#96ceb4',
    bills: '#ffeaa7',
    healthcare: '#dda0dd',
    education: '#98d8c8',
    travel: '#f7dc6f',
    salary: '#2ecc71',
    freelance: '#3498db',
    investment: '#9b59b6',
    gift: '#e74c3c',
    other: '#95a5a6',
  },

  // Account type colors
  accounts: {
    checking: '#2ecc71',
    savings: '#3498db',
    credit: '#e74c3c',
    cash: '#f39c12',
    investment: '#9b59b6',
  },
} as const;

// Dark mode colors
export const darkColors = {
  // Primary colors (same as light mode)
  primary: colors.primary,
  secondary: colors.secondary,
  accent: colors.accent,
  success: colors.success,
  warning: colors.warning,
  error: colors.error,
  info: colors.info,
  neutral: colors.neutral,

  // Dark background colors
  background: {
    primary: '#1c1c1e',     // New dark theme background (not pure black)
    secondary: '#2c2c2e',   // Slightly lighter for cards/surfaces
    tertiary: '#3a3a3c',    // Even lighter for subtle backgrounds
  },

  // Dark surface colors
  surface: {
    primary: '#000000',      // Pure black
    secondary: '#111111',    // Very dark gray
    elevated: '#1a1a1a',    // Dark gray for elevated surfaces
  },

  // Dark text colors
  text: {
    primary: '#ffffff',      // Pure white
    secondary: '#a1a1aa',    // Light gray
    tertiary: '#71717a',     // Medium gray
    inverse: '#000000',      // Black (inverse of light mode)
    disabled: '#52525b',     // Dark gray
  },

  // Additional text colors for compatibility
  textSecondary: '#a1a1aa',

  // Dark border colors
  border: {
    primary: '#27272a',      // Dark gray
    secondary: '#3f3f46',    // Medium dark gray
    focus: '#0ea5e9',        // Same as light mode
  },

  // Shadow colors
  shadow: '#000000',

  // Error background
  errorBackground: '#1f1f1f',

  // Category colors (same as light mode)
  categories: colors.categories,

  // Account type colors (same as light mode)
  accounts: colors.accounts,
} as const;

// Theme colors type - use a more flexible type
export type ThemeColors = {
  primary: typeof colors.primary;
  secondary: typeof colors.secondary;
  accent: typeof colors.accent;
  success: typeof colors.success;
  warning: typeof colors.warning;
  error: typeof colors.error;
  info: typeof colors.info;
  neutral: typeof colors.neutral;
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  surface: {
    primary: string;
    secondary: string;
    elevated: string;
  };
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
    disabled: string;
  };
  textSecondary: string;
  border: {
    primary: string;
    secondary: string;
    focus: string;
  };
  shadow: string;
  errorBackground: string;
  categories: typeof colors.categories;
  accounts: typeof colors.accounts;
};

// Function to get colors based on theme
export const getThemeColors = (isDark: boolean): ThemeColors => {
  return isDark ? darkColors as ThemeColors : colors as ThemeColors;
};

// Utility functions for colors
export const getColorWithOpacity = (color: string, opacity: number): string => {
  // Convert hex to rgba
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

export const getCategoryColor = (categoryName: string): string => {
  const normalizedName = categoryName.toLowerCase();

  if (normalizedName.includes('food') || normalizedName.includes('yemek')) {
    return colors.categories.food;
  }
  if (normalizedName.includes('transport') || normalizedName.includes('ulaşım')) {
    return colors.categories.transport;
  }
  if (normalizedName.includes('shopping') || normalizedName.includes('alışveriş')) {
    return colors.categories.shopping;
  }
  if (normalizedName.includes('entertainment') || normalizedName.includes('eğlence')) {
    return colors.categories.entertainment;
  }
  if (normalizedName.includes('bill') || normalizedName.includes('fatura')) {
    return colors.categories.bills;
  }
  if (normalizedName.includes('health') || normalizedName.includes('sağlık')) {
    return colors.categories.healthcare;
  }
  if (normalizedName.includes('education') || normalizedName.includes('eğitim')) {
    return colors.categories.education;
  }
  if (normalizedName.includes('travel') || normalizedName.includes('seyahat')) {
    return colors.categories.travel;
  }
  if (normalizedName.includes('salary') || normalizedName.includes('maaş')) {
    return colors.categories.salary;
  }
  if (normalizedName.includes('freelance') || normalizedName.includes('serbest')) {
    return colors.categories.freelance;
  }
  if (normalizedName.includes('investment') || normalizedName.includes('yatırım')) {
    return colors.categories.investment;
  }
  if (normalizedName.includes('gift') || normalizedName.includes('hediye')) {
    return colors.categories.gift;
  }

  return colors.categories.other;
};

export const getAccountColor = (accountType: string): string => {
  const type = accountType.toLowerCase() as keyof typeof colors.accounts;
  return colors.accounts[type] || colors.categories.other;
};