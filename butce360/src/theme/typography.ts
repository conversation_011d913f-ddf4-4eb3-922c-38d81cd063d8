import { TextStyle } from 'react-native';

// Typography system for Butce360 app - iOS Guidelines Compliant
export const typography = {
  // Font families - iOS San Francisco
  fonts: {
    regular: 'System', // iOS: San Francisco, Android: Roboto
    medium: 'System',
    semiBold: 'System',
    bold: 'System',
  },

  // iOS Typography Scale - Following Apple's Human Interface Guidelines
  sizes: {
    // iOS Standard Sizes
    largeTitle: 34,    // iOS Large Title
    title1: 28,        // iOS Title 1
    title2: 22,        // iOS Title 2
    title3: 20,        // iOS Title 3
    headline: 17,      // iOS Headline (Primary text)
    body: 17,          // iOS Body (Primary text)
    callout: 16,       // iOS Callout
    subhead: 15,       // iOS Subhead (Secondary text)
    footnote: 13,      // iOS Footnote (Tertiary text)
    caption1: 12,      // iOS Caption 1
    caption2: 11,      // iOS Caption 2

    // Legacy sizes for backward compatibility
    xs: 11,
    sm: 13,
    base: 17,
    lg: 20,
    xl: 22,
    '2xl': 28,
    '3xl': 34,
    '4xl': 40,
    '5xl': 48,
  },

  // iOS Line Heights - Optimized for readability
  lineHeights: {
    tight: 1.1,
    normal: 1.2,
    relaxed: 1.4,
    loose: 1.6,
  },

  // iOS Font Weights
  weights: {
    ultraLight: '100' as TextStyle['fontWeight'],
    thin: '200' as TextStyle['fontWeight'],
    light: '300' as TextStyle['fontWeight'],
    regular: '400' as TextStyle['fontWeight'],
    medium: '500' as TextStyle['fontWeight'],
    semibold: '600' as TextStyle['fontWeight'],
    bold: '700' as TextStyle['fontWeight'],
    heavy: '800' as TextStyle['fontWeight'],
    black: '900' as TextStyle['fontWeight'],
  },

  // iOS Text Styles - Following Apple's Human Interface Guidelines
  styles: {
    // iOS Typography Styles
    largeTitle: {
      fontSize: 34,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 41,
      letterSpacing: 0.37,
    } as TextStyle,

    title1: {
      fontSize: 28,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 34,
      letterSpacing: 0.36,
    } as TextStyle,

    title2: {
      fontSize: 22,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 28,
      letterSpacing: 0.35,
    } as TextStyle,

    title3: {
      fontSize: 20,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 25,
      letterSpacing: 0.38,
    } as TextStyle,

    headline: {
      fontSize: 17,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    body: {
      fontSize: 17,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    callout: {
      fontSize: 16,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 21,
      letterSpacing: -0.32,
    } as TextStyle,

    subhead: {
      fontSize: 15,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: -0.24,
    } as TextStyle,

    footnote: {
      fontSize: 13,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 18,
      letterSpacing: -0.08,
    } as TextStyle,

    caption1: {
      fontSize: 12,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 16,
      letterSpacing: 0,
    } as TextStyle,

    caption2: {
      fontSize: 11,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 13,
      letterSpacing: 0.07,
    } as TextStyle,

    // Legacy styles for backward compatibility
    h1: {
      fontSize: 34,
      fontWeight: '700' as TextStyle['fontWeight'],
      lineHeight: 41,
      letterSpacing: 0.37,
    } as TextStyle,

    h2: {
      fontSize: 28,
      fontWeight: '700' as TextStyle['fontWeight'],
      lineHeight: 34,
      letterSpacing: 0.36,
    } as TextStyle,

    h3: {
      fontSize: 22,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 28,
      letterSpacing: 0.35,
    } as TextStyle,

    h4: {
      fontSize: 20,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 25,
      letterSpacing: 0.38,
    } as TextStyle,

    h5: {
      fontSize: 17,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    h6: {
      fontSize: 15,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: -0.24,
    } as TextStyle,

    // Body text
    body1: {
      fontSize: 17,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    body2: {
      fontSize: 15,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: -0.24,
    } as TextStyle,

    // Captions and labels
    caption: {
      fontSize: 13,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 18,
      letterSpacing: -0.08,
    } as TextStyle,

    label: {
      fontSize: 15,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: -0.24,
    } as TextStyle,

    // Button text - iOS style
    button: {
      fontSize: 17,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    buttonSmall: {
      fontSize: 15,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: -0.24,
    } as TextStyle,

    buttonLarge: {
      fontSize: 20,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 25,
      letterSpacing: 0.38,
    } as TextStyle,

    // Currency and numbers - iOS optimized
    currency: {
      fontSize: 28,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 34,
      letterSpacing: 0.36,
    } as TextStyle,

    currencyLarge: {
      fontSize: 34,
      fontWeight: '700' as TextStyle['fontWeight'],
      lineHeight: 41,
      letterSpacing: 0.37,
    } as TextStyle,

    currencySmall: {
      fontSize: 17,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    // Navigation - iOS tab bar style
    tabLabel: {
      fontSize: 11,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 13,
      letterSpacing: 0.07,
    } as TextStyle,

    // Form elements - iOS style
    input: {
      fontSize: 17,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    inputLabel: {
      fontSize: 15,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: -0.24,
    } as TextStyle,

    inputError: {
      fontSize: 13,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 18,
      letterSpacing: -0.08,
    } as TextStyle,

    inputPlaceholder: {
      fontSize: 17,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    // Special text styles
    overline: {
      fontSize: 11,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 13,
      letterSpacing: 0.07,
      textTransform: 'uppercase' as TextStyle['textTransform'],
    } as TextStyle,

    subtitle1: {
      fontSize: 17,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    subtitle2: {
      fontSize: 15,
      fontWeight: '500' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: -0.24,
    } as TextStyle,

    // iOS specific styles
    navigationTitle: {
      fontSize: 17,
      fontWeight: '600' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    sectionHeader: {
      fontSize: 13,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 18,
      letterSpacing: -0.08,
      textTransform: 'uppercase' as TextStyle['textTransform'],
    } as TextStyle,

    listItem: {
      fontSize: 17,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 22,
      letterSpacing: -0.41,
    } as TextStyle,

    listItemDetail: {
      fontSize: 15,
      fontWeight: '400' as TextStyle['fontWeight'],
      lineHeight: 20,
      letterSpacing: -0.24,
    } as TextStyle,
  },
} as const;

// Utility function to get text style with color
export const getTextStyle = (
  style: keyof typeof typography.styles,
  color?: string
): TextStyle => {
  const baseStyle = typography.styles[style];
  return color ? { ...baseStyle, color } : baseStyle;
};