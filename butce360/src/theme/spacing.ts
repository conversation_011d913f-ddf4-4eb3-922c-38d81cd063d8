// Spacing system for consistent layout
export const spacing = {
  // Base spacing unit (4px)
  unit: 4,

  // Spacing scale
  xs: 4,    // 4px
  sm: 8,    // 8px
  md: 12,   // 12px
  lg: 16,   // 16px
  xl: 20,   // 20px
  '2xl': 24, // 24px
  '3xl': 32, // 32px
  '4xl': 40, // 40px
  '5xl': 48, // 48px
  '6xl': 64, // 64px

  // Semantic spacing
  screenPadding: 16,
  cardPadding: 16,
  sectionSpacing: 24,
  componentSpacing: 12,

  // Button spacing
  buttonPaddingVertical: 12,
  buttonPaddingHorizontal: 24,
  buttonSpacing: 8,

  // Input spacing
  inputPaddingVertical: 12,
  inputPaddingHorizontal: 16,
  inputSpacing: 16,

  // List spacing
  listItemPadding: 16,
  listItemSpacing: 8,

  // Header spacing
  headerPadding: 16,
  headerHeight: 56,

  // Tab bar spacing
  tabBarHeight: 60,
  tabBarPadding: 8,

  // Modal spacing
  modalPadding: 24,
  modalSpacing: 16,

  // Card spacing
  cardRadius: 12,
  cardSpacing: 16,

  // Icon spacing
  iconSize: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 32,
    xl: 40,
  },

  // Avatar spacing
  avatarSize: {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 64,
  },
} as const;

// Utility functions for spacing
export const getSpacing = (multiplier: number): number => {
  return spacing.unit * multiplier;
};

export const getHorizontalSpacing = (left: number, right?: number): object => {
  return {
    paddingLeft: left,
    paddingRight: right ?? left,
  };
};

export const getVerticalSpacing = (top: number, bottom?: number): object => {
  return {
    paddingTop: top,
    paddingBottom: bottom ?? top,
  };
};

export const getAllSpacing = (
  top: number,
  right?: number,
  bottom?: number,
  left?: number
): object => {
  return {
    paddingTop: top,
    paddingRight: right ?? top,
    paddingBottom: bottom ?? top,
    paddingLeft: left ?? right ?? top,
  };
};

export const getMarginSpacing = (
  top: number,
  right?: number,
  bottom?: number,
  left?: number
): object => {
  return {
    marginTop: top,
    marginRight: right ?? top,
    marginBottom: bottom ?? top,
    marginLeft: left ?? right ?? top,
  };
};