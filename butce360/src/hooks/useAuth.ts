import { useAuth as useAuthContext } from '../context/AuthContext';

// Re-export the useAuth hook from context for convenience
export const useAuth = useAuthContext;

// Additional auth-related hooks

// Hook to check if user is authenticated
export const useIsAuthenticated = () => {
  const { state } = useAuth();
  return state.isAuthenticated;
};

// Hook to check if user is in guest mode
export const useIsGuest = () => {
  const { state } = useAuth();
  return state.isGuest;
};

// Hook to get current user
export const useCurrentUser = () => {
  const { state } = useAuth();
  return state.user;
};

// Hook to check if auth is loading
export const useAuthLoading = () => {
  const { state } = useAuth();
  return state.isLoading;
};

// Hook to get auth error
export const useAuthError = () => {
  const { state } = useAuth();
  return state.error;
};

// Hook for auth guard (redirect to login if not authenticated)
export const useAuthGuard = () => {
  const { state } = useAuth();

  const requireAuth = (callback?: () => void) => {
    if (!state.isAuthenticated && !state.isGuest) {
      // In a real app, this would navigate to login screen
      console.log('[AuthGuard] Authentication required');
      return false;
    }

    if (callback) {
      callback();
    }
    return true;
  };

  return { requireAuth };
};

// Hook for guest guard (show login prompt for guest users)
export const useGuestGuard = () => {
  const { state } = useAuth();

  const requireAuth = (callback?: () => void) => {
    if (state.isGuest) {
      // In a real app, this would show a login prompt
      console.log('[GuestGuard] Login required for this action');
      return false;
    }

    if (callback) {
      callback();
    }
    return true;
  };

  return { requireAuth };
};