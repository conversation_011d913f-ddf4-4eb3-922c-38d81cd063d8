import { useMemo } from 'react';
import { StyleSheet } from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { getThemeColors, ThemeColors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { typography } from '../theme/typography';

// Types for theme objects
type SpacingType = typeof spacing;
type TypographyType = typeof typography;

// Hook to get themed colors
export const useThemedColors = (): ThemeColors => {
  const { state } = useTheme();
  return useMemo(() => getThemeColors(state.isDark), [state.isDark]);
};

// Hook to create themed styles
export const useThemedStyles = <T extends StyleSheet.NamedStyles<T>>(
  createStyles: (colors: ThemeColors, spacing: SpacingType, typography: TypographyType) => T
): T => {
  const colors = useThemedColors();

  return useMemo(
    () => createStyles(colors, spacing, typography),
    [colors, createStyles]
  );
};

// Hook to get theme state
export const useThemeState = () => {
  const { state } = useTheme();
  return state;
};

// Hook to get theme actions
export const useThemeActions = () => {
  const { setThemeMode, toggleTheme } = useTheme();
  return { setThemeMode, toggleTheme };
};
