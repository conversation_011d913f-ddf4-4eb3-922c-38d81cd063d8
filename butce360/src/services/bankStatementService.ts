import { apiClient } from './apiClient';

export interface BankStatementEntry {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  categoryId?: string;
  accountId?: string;
}

export interface BankStatementUploadResponse {
  entries: BankStatementEntry[];
}

export interface BankStatementImportRequest {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  categoryId: string;
  accountId: string;
}

export interface BankStatementImportResponse {
  transactions: any[];
}

export interface UploadBankStatementParams {
  file: {
    uri: string;
    type: string;
    name: string;
  };
  bank: 'vakifbank' | 'enpara' | 'garanti';
}

class BankStatementService {
  async uploadBankStatement(params: UploadBankStatementParams): Promise<BankStatementUploadResponse> {
    console.log('[BankStatementService] Uploading bank statement:', {
      fileName: params.file.name,
      fileType: params.file.type,
      bank: params.bank,
    });

    const formData = new FormData();

    // Add file to form data
    formData.append('file', {
      uri: params.file.uri,
      type: params.file.type,
      name: params.file.name,
    } as any);

    // Add bank parameter
    formData.append('bank', params.bank);

    console.log('[BankStatementService] FormData prepared:', formData);

    try {
      const response = await apiClient.post('/bank-statements/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('[BankStatementService] Upload successful:', response.data);
      return response.data.data;
    } catch (error) {
      console.error('[BankStatementService] Upload failed:', error);
      throw error;
    }
  }

  async importBankStatementEntries(entries: BankStatementImportRequest[]): Promise<BankStatementImportResponse> {
    const response = await apiClient.post('/bank-statements/import', entries);
    return response.data.data;
  }

  getSupportedBanks(): Array<{ key: string; name: string }> {
    return [
      { key: 'vakifbank', name: 'VakıfBank' },
      { key: 'enpara', name: 'Enpara.com' },
      { key: 'garanti', name: 'Garanti BBVA' },
    ];
  }
}

export const bankStatementService = new BankStatementService();
