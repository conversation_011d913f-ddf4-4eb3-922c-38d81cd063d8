import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { authService } from './authService';

export class GoogleSignInService {
  private static instance: GoogleSignInService;

  private constructor() {}

  static getInstance(): GoogleSignInService {
    if (!GoogleSignInService.instance) {
      GoogleSignInService.instance = new GoogleSignInService();
    }
    return GoogleSignInService.instance;
  }

  // Configure Google Sign-In
  configure() {
    GoogleSignin.configure({
      webClientId: 'YOUR_WEB_CLIENT_ID', // From Google Cloud Console
      offlineAccess: true,
      hostedDomain: '',
      forceCodeForRefreshToken: true,
    });
  }

  // Check if Google Play Services are available
  async isPlayServicesAvailable(): Promise<boolean> {
    try {
      await GoogleSignin.hasPlayServices();
      return true;
    } catch (error) {
      console.error('[GoogleSignInService] Play Services not available:', error);
      return false;
    }
  }

  // Sign in with Google
  async signIn(): Promise<{ success: boolean; user?: any; error?: string }> {
    try {
      // Check if Play Services are available
      const playServicesAvailable = await this.isPlayServicesAvailable();
      if (!playServicesAvailable) {
        return {
          success: false,
          error: 'Google Play Services gerekli'
        };
      }

      // Sign in
      const userInfo = await GoogleSignin.signIn();
      console.log('[GoogleSignInService] Google sign-in successful:', userInfo);

      // Send Google token to backend for authentication
      const authResult = await this.authenticateWithBackend(userInfo);
      
      if (authResult.success) {
        return {
          success: true,
          user: authResult.user
        };
      } else {
        return {
          success: false,
          error: authResult.error || 'Backend authentication failed'
        };
      }
    } catch (error: any) {
      console.error('[GoogleSignInService] Sign-in error:', error);
      
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        return {
          success: false,
          error: 'Giriş iptal edildi'
        };
      } else if (error.code === statusCodes.IN_PROGRESS) {
        return {
          success: false,
          error: 'Giriş işlemi devam ediyor'
        };
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        return {
          success: false,
          error: 'Google Play Services mevcut değil'
        };
      } else {
        return {
          success: false,
          error: 'Google ile giriş yapılamadı'
        };
      }
    }
  }

  // Authenticate with backend using Google token
  private async authenticateWithBackend(userInfo: any): Promise<{ success: boolean; user?: any; error?: string }> {
    try {
      // Extract Google user info
      const googleUser = {
        id: userInfo.user.id,
        email: userInfo.user.email,
        name: userInfo.user.name,
        photo: userInfo.user.photo,
        idToken: userInfo.idToken,
      };

      // Send to backend for authentication
      const response = await authService.googleSignIn(googleUser);
      
      return {
        success: true,
        user: response
      };
    } catch (error) {
      console.error('[GoogleSignInService] Backend authentication error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Backend authentication failed'
      };
    }
  }

  // Sign out
  async signOut(): Promise<void> {
    try {
      await GoogleSignin.signOut();
      console.log('[GoogleSignInService] Google sign-out successful');
    } catch (error) {
      console.error('[GoogleSignInService] Sign-out error:', error);
    }
  }

  // Get current user
  async getCurrentUser(): Promise<any> {
    try {
      const userInfo = await GoogleSignin.signInSilently();
      return userInfo;
    } catch (error) {
      console.error('[GoogleSignInService] Get current user error:', error);
      return null;
    }
  }

  // Check if user is signed in
  async isSignedIn(): Promise<boolean> {
    try {
      const userInfo = await GoogleSignin.getCurrentUser();
      return userInfo !== null;
    } catch (error) {
      console.error('[GoogleSignInService] Check signed in error:', error);
      return false;
    }
  }
}

export const googleSignInService = GoogleSignInService.getInstance();
