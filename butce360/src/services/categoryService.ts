import { apiClient } from './api';
import { authService } from './authService';
import { Category, CreateCategoryRequest, UpdateCategoryRequest } from '../types/models';
import { DEFAULT_CATEGORIES, getDefaultCategoriesByType } from '../constants/defaultCategories';

export class CategoryService {
  private static instance: CategoryService;

  private constructor() {}

  static getInstance(): CategoryService {
    if (!CategoryService.instance) {
      CategoryService.instance = new CategoryService();
    }
    return CategoryService.instance;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await authService.getStoredToken();
    if (!token) {
      throw new Error('No authentication token available');
    }
    return {
      'Authorization': `Bearer ${token}`
    };
  }

  // Get all categories
  async getCategories(type?: 'income' | 'expense'): Promise<Category[]> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: return default categories
        console.log('[CategoryService] Guest mode: returning default categories');
        return type ? getDefaultCategoriesByType(type) : DEFAULT_CATEGORIES;
      }

      // Authenticated user: fetch from API
      const headers = await this.getAuthHeaders();
      const params = type ? `?type=${type}` : '';
      const response = await apiClient.get<any>(`/categories${params}`, headers);

      console.log('[CategoryService] Fetched categories:', response);

      // Transform API response to match frontend format
      const categories = response.data?.data || response.data || [];
      const transformedCategories = Array.isArray(categories) ? categories.map((category: any) => ({
        id: category.id,
        name: category.name,
        type: category.type,
        icon: category.icon,
        color: category.color || this.getDefaultColor(category.type),
        isDefault: category.is_default || false,
        createdAt: category.created_at,
        updatedAt: category.updated_at,
      })) : [];

      return transformedCategories;
    } catch (error) {
      console.error('[CategoryService] Error fetching categories:', error);
      // Fallback to default categories if API fails
      console.log('[CategoryService] Falling back to default categories');
      return type ? getDefaultCategoriesByType(type) : DEFAULT_CATEGORIES;
    }
  }

  // Get default color for category type
  private getDefaultColor(type: string): string {
    return type === 'income' ? '#22c55e' : '#ef4444';
  }

  // Get category by ID
  async getCategory(id: string): Promise<Category> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<Category>(`/categories/${id}`, headers);

      console.log('[CategoryService] Fetched category:', response.data);
      return response.data;
    } catch (error) {
      console.error('[CategoryService] Error fetching category:', error);
      throw error;
    }
  }

  // Create new category
  async createCategory(categoryData: CreateCategoryRequest): Promise<Category> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.post<Category>('/categories', categoryData, headers);

      console.log('[CategoryService] Created category:', response.data);
      return response.data;
    } catch (error) {
      console.error('[CategoryService] Error creating category:', error);
      throw error;
    }
  }

  // Update category
  async updateCategory(id: string, categoryData: UpdateCategoryRequest): Promise<Category> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.put<Category>(`/categories/${id}`, categoryData, headers);

      console.log('[CategoryService] Updated category:', response.data);
      return response.data;
    } catch (error) {
      console.error('[CategoryService] Error updating category:', error);
      throw error;
    }
  }

  // Delete category
  async deleteCategory(id: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      await apiClient.delete(`/categories/${id}`, headers);

      console.log('[CategoryService] Deleted category:', id);
    } catch (error) {
      console.error('[CategoryService] Error deleting category:', error);
      throw error;
    }
  }


}

// Export singleton instance
export const categoryService = CategoryService.getInstance();
