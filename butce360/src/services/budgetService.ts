import { apiClient } from './api';
import { authService } from './authService';
import { storage } from './storage';

export interface Budget {
  id: string;
  name: string;
  amount: number;
  spent: number;
  remaining: number;
  currency: string;
  categoryId?: string;
  categoryName?: string;
  period: 'monthly' | 'yearly';
  startDate: string;
  endDate: string;
  color?: string; // Color for budget visualization
  createdAt: string;
  updatedAt: string;
}

export interface CreateBudgetRequest {
  name: string;
  amount: number;
  category_id?: string;
  period: 'monthly' | 'yearly';
  color?: string;
}

export interface UpdateBudgetRequest {
  name?: string;
  amount?: number;
  category_id?: string;
  period?: 'monthly' | 'yearly';
  startDate?: string;
  endDate?: string;
}

export class BudgetService {
  private static instance: BudgetService;

  private constructor() {}

  static getInstance(): BudgetService {
    if (!BudgetService.instance) {
      BudgetService.instance = new BudgetService();
    }
    return BudgetService.instance;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await authService.getStoredToken();
    if (!token) {
      throw new Error('No authentication token available');
    }
    return {
      'Authorization': `Bearer ${token}`
    };
  }

  // Get all budgets
  async getBudgets(): Promise<Budget[]> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: return local budgets
        console.log('[BudgetService] Guest mode: returning local budgets');
        const localBudgets = await storage.getObject<Budget[]>('local_budgets') || [];
        return localBudgets;
      }

      // Authenticated user: fetch from API
      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<Budget[]>('/budget', headers);

      console.log('[BudgetService] Fetched budgets:', response.data);
      return response.data;
    } catch (error) {
      console.error('[BudgetService] Error fetching budgets:', error);

      // Fallback to local budgets if API fails
      console.log('[BudgetService] Falling back to local budgets');
      const localBudgets = await storage.getObject<Budget[]>('local_budgets') || [];
      if (localBudgets.length > 0) {
        return localBudgets;
      }

      // Return mock data with colors for development
      return [
        {
          id: '1',
          name: 'Yiyecek & İçecek',
          amount: 2000,
          spent: 1250,
          remaining: 750,
          currency: 'TRY',
          categoryId: 'food',
          categoryName: 'Yiyecek & İçecek',
          period: 'monthly',
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          color: '#ff6b6b',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          name: 'Ulaşım',
          amount: 800,
          spent: 450,
          remaining: 350,
          currency: 'TRY',
          categoryId: 'transport',
          categoryName: 'Ulaşım',
          period: 'monthly',
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          color: '#4ecdc4',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: '3',
          name: 'Eğlence',
          amount: 1000,
          spent: 1200,
          remaining: -200,
          currency: 'TRY',
          categoryId: 'entertainment',
          categoryName: 'Eğlence',
          period: 'monthly',
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          color: '#45b7d1',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ];
    }
  }

  // Create new budget
  async createBudget(budgetData: CreateBudgetRequest): Promise<Budget> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: save to local storage
        console.log('[BudgetService] Guest mode: saving budget locally');

        const localBudget: Budget = {
          id: Date.now().toString(), // Simple ID generation
          name: budgetData.name,
          amount: budgetData.amount,
          spent: 0,
          remaining: budgetData.amount,
          currency: 'TRY',
          categoryId: budgetData.category_id,
          categoryName: 'Kategori', // Will be resolved later
          period: budgetData.period,
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days later
          color: budgetData.color,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        // Get existing budgets from local storage
        const existingBudgets = await storage.getObject<Budget[]>('local_budgets') || [];
        existingBudgets.push(localBudget);

        // Save back to local storage
        await storage.setObject('local_budgets', existingBudgets);

        return localBudget;
      }

      // Authenticated user: save to API
      const headers = await this.getAuthHeaders();
      const response = await apiClient.post<Budget>('/budget', budgetData, headers);

      console.log('[BudgetService] Created budget:', response.data);
      return response.data;
    } catch (error) {
      console.error('[BudgetService] Error creating budget:', error);
      throw error;
    }
  }

  // Update budget
  async updateBudget(id: string, budgetData: UpdateBudgetRequest): Promise<Budget> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.put<Budget>(`/budget/${id}`, budgetData, headers);

      console.log('[BudgetService] Updated budget:', response.data);
      return response.data;
    } catch (error) {
      console.error('[BudgetService] Error updating budget:', error);
      throw error;
    }
  }

  // Delete budget
  async deleteBudget(id: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      await apiClient.delete(`/budget/${id}`, headers);

      console.log('[BudgetService] Deleted budget:', id);
    } catch (error) {
      console.error('[BudgetService] Error deleting budget:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const budgetService = BudgetService.getInstance();
