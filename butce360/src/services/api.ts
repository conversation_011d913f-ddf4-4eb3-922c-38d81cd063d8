import { ApiResponse, ApiError } from '../types/models';

// Environment configuration
const API_CONFIG = {
  BASE_URL: __DEV__
    ? 'http://localhost:8008/api/v1'
    : 'https://app.butce360.com/api/v1',
  TIMEOUT: 30000,
  DEBUG: __DEV__,
};

// Debug logging utility
const debugLog = (...args: any[]) => {
  if (API_CONFIG.DEBUG) {
    console.log('[API]', ...args);
  }
};

export class ApiClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    debugLog('ApiClient initialized with baseURL:', this.baseURL);
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

    const requestOptions: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    };

    try {
      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Request timeout'));
        }, this.timeout);
      });

      // Make the actual request
      const fetchPromise = fetch(url, requestOptions);

      // Race between fetch and timeout
      const response = await Promise.race([fetchPromise, timeoutPromise]);

      // Parse response
      let responseData: any;
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      debugLog('API Response:', {
        url,
        status: response.status,
        data: responseData
      });

      // Handle non-2xx responses
      if (!response.ok) {
        const error: ApiError = {
          message: responseData?.message || responseData?.error || 'Request failed',
          status: response.status,
          code: responseData?.code,
          details: responseData,
        };
        throw error;
      }

      // Return successful response
      return {
        data: responseData?.data || responseData,
        message: responseData?.message,
        success: true,
        status: response.status,
      };

    } catch (error) {
      debugLog('API Error:', error);

      if (error instanceof Error) {
        if (error.message === 'Request timeout') {
          throw {
            message: 'Request timeout. Please check your internet connection.',
            status: 408,
            code: 'TIMEOUT',
          } as ApiError;
        }

        if (error.message === 'Network request failed') {
          throw {
            message: 'Network error. Please check your internet connection.',
            status: 0,
            code: 'NETWORK_ERROR',
          } as ApiError;
        }
      }

      // Re-throw ApiError as is
      if (typeof error === 'object' && error !== null && 'status' in error) {
        throw error as ApiError;
      }

      // Wrap unknown errors
      throw {
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        status: 500,
        code: 'UNKNOWN_ERROR',
        details: error,
      } as ApiError;
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'GET',
      headers,
    });
  }

  async post<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PUT',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PATCH',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'DELETE',
      headers,
    });
  }

  // Authenticated requests
  async authenticatedRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    token?: string
  ): Promise<ApiResponse<T>> {
    const authHeaders: Record<string, string> = {};

    if (token) {
      authHeaders.Authorization = `Bearer ${token}`;
    }

    return this.makeRequest<T>(endpoint, {
      ...options,
      headers: {
        ...options.headers,
        ...authHeaders,
      },
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();