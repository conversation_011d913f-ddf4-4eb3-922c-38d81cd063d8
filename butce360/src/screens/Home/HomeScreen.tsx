import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  RefreshControl,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { storage } from '../../services/storage';
import { localTransactionService, LocalTransaction } from '../../services/localTransactionService';

const HomeScreen: React.FC = () => {
  const colors = useThemedColors();
  const insets = useSafeAreaInsets();
  
  const [currentDate, setCurrentDate] = useState(new Date());
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [activeFilter, setActiveFilter] = useState<'all' | 'income' | 'expense'>('all');
  const [transactions, setTransactions] = useState<LocalTransaction[]>([]);
  const [_loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const loadBalanceVisibility = async () => {
    try {
      const visibility = await storage.getItem('balance_visibility');
      if (visibility !== null) {
        setBalanceVisible(visibility === 'true');
      }
    } catch (error) {
      console.error('Error loading balance visibility:', error);
    }
  };

  const loadTransactions = useCallback(async () => {
    try {
      setLoading(true);
      const filter = {
        month: currentDate.getMonth(),
        year: currentDate.getFullYear(),
        ...(activeFilter !== 'all' && { type: activeFilter }),
      };

      const loadedTransactions = await localTransactionService.getTransactions(filter);
      setTransactions(loadedTransactions);
    } catch (error) {
      console.error('Error loading transactions:', error);
    } finally {
      setLoading(false);
    }
  }, [currentDate, activeFilter]);

  useEffect(() => {
    loadBalanceVisibility();
    loadTransactions();
  }, [currentDate, activeFilter, loadTransactions]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTransactions();
    setRefreshing(false);
  };

  const toggleBalanceVisibility = async () => {
    const newVisibility = !balanceVisible;
    setBalanceVisible(newVisibility);
    try {
      await storage.setItem('balance_visibility', newVisibility.toString());
    } catch (error) {
      console.error('Error saving balance visibility:', error);
    }
  };

  const getMonthYear = () => {
    const months = [
      'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
      'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
    ];
    return `${months[currentDate.getMonth()]}, ${currentDate.getFullYear()}`;
  };

  const calculateBalance = () => {
    return transactions.reduce((sum, transaction) => {
      return sum + (transaction.type === 'income' ? transaction.amount : -transaction.amount);
    }, 0);
  };

  const formatTransactionDate = (dateString: string) => {
    const date = new Date(dateString);
    const months = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
    return `${date.getDate()} ${months[date.getMonth()]}`;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);

    if (activeFilter !== 'all') {
      setActiveFilter('all');
    }
  };

  const balance = calculateBalance();

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar 
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background.primary}
      />
      
      {/* Header with Month Navigation */}
      <View style={[styles.header, { paddingTop: insets.top + 16 }]}>
        <TouchableOpacity
          onPress={() => navigateMonth('prev')}
          style={[styles.navButton, { backgroundColor: colors.background.secondary }]}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-back-outline" size={22} color={colors.text.primary} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.monthContainer} activeOpacity={0.8}>
          <Text style={[styles.monthText, { color: colors.text.primary }]}>
            {getMonthYear()}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => navigateMonth('next')}
          style={[styles.navButton, { backgroundColor: colors.background.secondary }]}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-forward-outline" size={22} color={colors.text.primary} />
        </TouchableOpacity>
      </View>

      {/* Balance Section */}
      <View style={styles.balanceSection}>
        <TouchableOpacity onPress={toggleBalanceVisibility} style={styles.balanceContainer}>
          <Text style={[styles.balanceAmount, { color: colors.text.primary }]}>
            {balanceVisible ? `₺${balance.toFixed(2)}` : '••••••'}
          </Text>
          <View style={styles.balanceToggle}>
            <Ionicons 
              name={balanceVisible ? "eye-outline" : "eye-off-outline"} 
              size={16} 
              color={colors.text.secondary} 
              style={styles.eyeIcon}
            />
            <Text style={[styles.balanceLabel, { color: colors.text.secondary }]}>
              {balanceVisible ? 'Gizle' : 'Göster'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        {[
          { key: 'all', label: 'Tümü' },
          { key: 'income', label: 'Gelir' },
          { key: 'expense', label: 'Gider' },
        ].map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterTab,
              activeFilter === filter.key && [styles.filterTabActive, { backgroundColor: colors.primary[500] }]
            ]}
            onPress={() => setActiveFilter(filter.key as any)}
          >
            <Text style={[
              styles.filterText,
              { color: activeFilter === filter.key ? colors.background.secondary : colors.text.secondary }
            ]}>
              {filter.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Transactions List */}
      <ScrollView
        style={styles.transactionsList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
      >
        {transactions.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="document-text-outline" size={48} color={colors.text.secondary} style={styles.sectionMargin} />
            <Text style={[styles.emptyStateTitle, { color: colors.text.primary }]}>
              Henüz işlem yok
            </Text>
            <Text style={[styles.emptyStateSubtitle, { color: colors.text.secondary }]}>
              {activeFilter === 'all'
                ? 'Bu ay için henüz işlem eklenmemiş'
                : activeFilter === 'income'
                  ? 'Bu ay için henüz gelir eklenmemiş'
                  : 'Bu ay için henüz gider eklenmemiş'
              }
            </Text>
          </View>
        ) : (
          transactions.map((transaction) => (
            <View key={transaction.id} style={[styles.transactionItem, { backgroundColor: colors.background.secondary }]}>
              <View style={styles.transactionLeft}>
                <View style={[styles.transactionIcon, { backgroundColor: colors.background.tertiary }]}>
                  <Text style={styles.transactionIconText}>
                    {transaction.type === 'income' ? '+' : '−'}
                  </Text>
                </View>
                <View style={styles.transactionInfo}>
                  <Text style={[styles.transactionTitle, { color: colors.text.primary }]}>
                    {transaction.title}
                  </Text>
                  <Text style={[styles.transactionDate, { color: colors.text.secondary }]}>
                    {formatTransactionDate(transaction.date)} • {transaction.category}
                  </Text>
                </View>
              </View>
              <View style={styles.transactionRight}>
                <Text style={[
                  styles.transactionAmount,
                  { color: transaction.type === 'income' ? colors.success[500] : colors.error[500] }
                ]}>
                  {transaction.type === 'expense' ? '-' : '+'}₺{transaction.amount.toFixed(2)}
                </Text>
                <TouchableOpacity style={styles.transactionMenu}>
                  <Ionicons name="ellipsis-vertical" size={18} color={colors.text.secondary} />
                </TouchableOpacity>
              </View>
            </View>
          ))
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  navButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  monthContainer: { paddingHorizontal: 16, paddingVertical: 8, borderRadius: 8 },
  monthText: { ...typography.styles.headline },
  balanceSection: { paddingHorizontal: 20, paddingBottom: 30, alignItems: 'center' },
  balanceContainer: { alignItems: 'center' },
  balanceAmount: { ...typography.styles.largeTitle, fontWeight: '300', marginBottom: 8 },
  balanceToggle: { flexDirection: 'row', alignItems: 'center' },
  balanceLabel: { ...typography.styles.footnote, fontWeight: '500' },
  filterContainer: { flexDirection: 'row', paddingHorizontal: 20, marginBottom: 20, gap: 12 },
  filterTab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  filterTabActive: {},
  filterText: { ...typography.styles.button, textTransform: 'none' },
  transactionsList: { flex: 1, paddingHorizontal: 20 },
  emptyState: { alignItems: 'center', justifyContent: 'center', paddingVertical: 60, paddingHorizontal: 40 },
  emptyStateTitle: { ...typography.styles.headline, marginBottom: 8, textAlign: 'center' },
  emptyStateSubtitle: { ...typography.styles.subhead, textAlign: 'center' },
  transactionItem: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', padding: 16, borderRadius: 12, marginBottom: 8 },
  transactionLeft: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  transactionIcon: { width: 32, height: 32, borderRadius: 8, alignItems: 'center', justifyContent: 'center', marginRight: 12 },
  transactionIconText: { fontSize: 16 },
  transactionInfo: { flex: 1 },
  transactionTitle: { ...typography.styles.body, marginBottom: 2 },
  transactionDate: { ...typography.styles.subhead },
  transactionRight: { flexDirection: 'row', alignItems: 'center' },
  transactionAmount: { ...typography.styles.callout, fontWeight: '500', marginRight: 8 },
  transactionMenu: { padding: 4 },
  eyeIcon: { marginRight: 6 },
  sectionMargin: { marginBottom: 16 },
});

export default HomeScreen;
