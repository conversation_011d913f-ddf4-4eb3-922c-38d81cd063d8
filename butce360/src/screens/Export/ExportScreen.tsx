import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Alert,
  ScrollView,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

interface ExportScreenProps {
  onNavigate?: (screen: string) => void;
}

const ExportScreen: React.FC<ExportScreenProps> = ({ onNavigate }) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);
  
  const [exportOptions, setExportOptions] = useState({
    includeTransactions: true,
    includeCategories: true,
    includeAccounts: true,
    includeBudgets: false,
    includeReports: true,
    dateRange: 'all', // 'all', 'thisMonth', 'thisYear', 'custom'
  });
  const [exporting, setExporting] = useState(false);

  const handleExport = () => {
    setExporting(true);
    
    // Simulate export process
    setTimeout(() => {
      setExporting(false);
      Alert.alert(
        'Dışa Aktarma Tamamlandı',
        'Verileriniz PDF formatında başarıyla dışa aktarıldı!',
        [
          { text: 'Tamam' }
        ]
      );
    }, 3000);
  };

  const toggleOption = (key: string) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const setDateRange = (range: string) => {
    setExportOptions(prev => ({
      ...prev,
      dateRange: range
    }));
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => onNavigate?.('Menu')}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Veri Dışa Aktarma</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info Section */}
        <View style={styles.section}>
          <View style={styles.infoCard}>
            <View style={styles.infoIconContainer}>
              <Ionicons name="document-outline" size={32} color={colors.primary[500]} />
            </View>
            <Text style={styles.infoTitle}>PDF Raporu Oluştur</Text>
            <Text style={styles.infoText}>
              Tüm finansal verilerinizi detaylı PDF raporu olarak dışa aktarın.
            </Text>
          </View>
        </View>

        {/* Export Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dışa Aktarılacak Veriler</Text>
          <View style={styles.optionsList}>
            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="swap-horizontal-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>İşlemler</Text>
              </View>
              <Switch
                value={exportOptions.includeTransactions}
                onValueChange={() => toggleOption('includeTransactions')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeTransactions ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="folder-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Kategoriler</Text>
              </View>
              <Switch
                value={exportOptions.includeCategories}
                onValueChange={() => toggleOption('includeCategories')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeCategories ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="business-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Hesaplar</Text>
              </View>
              <Switch
                value={exportOptions.includeAccounts}
                onValueChange={() => toggleOption('includeAccounts')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeAccounts ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="target-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Bütçeler</Text>
              </View>
              <Switch
                value={exportOptions.includeBudgets}
                onValueChange={() => toggleOption('includeBudgets')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeBudgets ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="bar-chart-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Raporlar</Text>
              </View>
              <Switch
                value={exportOptions.includeReports}
                onValueChange={() => toggleOption('includeReports')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeReports ? colors.primary[500] : colors.neutral[500]}
              />
            </View>
          </View>
        </View>

        {/* Date Range */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tarih Aralığı</Text>
          <View style={styles.dateRangeOptions}>
            {[
              { key: 'all', label: 'Tüm Veriler', icon: 'infinite-outline' },
              { key: 'thisMonth', label: 'Bu Ay', icon: 'calendar-outline' },
              { key: 'thisYear', label: 'Bu Yıl', icon: 'calendar-outline' },
              { key: 'custom', label: 'Özel Aralık', icon: 'options-outline' },
            ].map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.dateRangeOption,
                  exportOptions.dateRange === option.key && styles.dateRangeOptionActive
                ]}
                onPress={() => setDateRange(option.key)}
              >
                <Ionicons 
                  name={option.icon} 
                  size={20} 
                  color={exportOptions.dateRange === option.key ? colors.primary[500] : colors.text.secondary} 
                />
                <Text style={[
                  styles.dateRangeOptionText,
                  exportOptions.dateRange === option.key && styles.dateRangeOptionTextActive
                ]}>
                  {option.label}
                </Text>
                {exportOptions.dateRange === option.key && (
                  <Ionicons name="checkmark-circle" size={20} color={colors.primary[500]} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Export Button */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[
              styles.exportButton,
              { backgroundColor: exporting ? colors.neutral[400] : colors.primary[500] }
            ]}
            onPress={handleExport}
            disabled={exporting}
          >
            {exporting ? (
              <>
                <Ionicons name="sync-outline" size={20} color={colors.background.secondary} />
                <Text style={[styles.exportButtonText, { color: colors.background.secondary }]}>
                  PDF Oluşturuluyor...
                </Text>
              </>
            ) : (
              <>
                <Ionicons name="download-outline" size={20} color={colors.background.secondary} />
                <Text style={[styles.exportButtonText, { color: colors.background.secondary }]}>
                  PDF Olarak Dışa Aktar
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        {/* Info */}
        <View style={styles.section}>
          <View style={styles.infoBox}>
            <Ionicons name="information-circle-outline" size={20} color={colors.primary[500]} />
            <Text style={styles.infoBoxText}>
              PDF raporu tüm seçtiğiniz verileri içerecek ve cihazınıza indirilecektir.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.lg,
  },
  infoCard: {
    backgroundColor: colors.background.secondary,
    padding: spacing.xl,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  infoIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  infoTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  infoText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsList: {
    backgroundColor: colors.background.secondary,
    borderRadius: spacing.cardRadius,
    overflow: 'hidden',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionText: {
    ...typography.styles.body2,
    color: colors.text.primary,
    marginLeft: spacing.md,
  },
  dateRangeOptions: {
    gap: spacing.sm,
  },
  dateRangeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    borderWidth: 1,
    borderColor: colors.border.primary,
  },
  dateRangeOptionActive: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  dateRangeOptionText: {
    ...typography.styles.body2,
    color: colors.text.primary,
    marginLeft: spacing.md,
    flex: 1,
  },
  dateRangeOptionTextActive: {
    color: colors.primary[500],
    fontWeight: '600',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.buttonPaddingVertical,
    borderRadius: spacing.cardRadius,
  },
  exportButtonText: {
    ...typography.styles.button,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.primary[50],
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary[500],
  },
  infoBoxText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    marginLeft: spacing.md,
    flex: 1,
    lineHeight: 20,
  },
});

export default ExportScreen;
