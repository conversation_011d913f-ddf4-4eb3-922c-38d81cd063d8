import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Switch,
  Alert,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { useThemedStyles, useThemeActions, useThemeState, useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
// import dataService from '../../services/dataService'; // Removed - not implemented

interface SettingItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  showArrow?: boolean;
  rightComponent?: React.ReactNode;
  styles: any; // Add styles prop
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  showArrow = true,
  rightComponent,
  styles,
}) => (
  <TouchableOpacity style={styles.settingItem} onPress={onPress} disabled={!onPress}>
    <View style={styles.settingItemLeft}>
      <View style={styles.settingIconContainer}>
        <Ionicons name={icon} size={20} color="#6b7280" />
      </View>
      <View style={styles.settingTextContainer}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
    </View>
    <View style={styles.settingItemRight}>
      {rightComponent}
      {showArrow && onPress && (
        <Text style={styles.settingArrow}>›</Text>
      )}
    </View>
  </TouchableOpacity>
);

interface SettingsScreenProps {
  onNavigate?: (screen: string) => void;
}

const SettingsScreen: React.FC<SettingsScreenProps> = ({ onNavigate: _onNavigate }) => {
  const { setThemeMode } = useThemeActions();
  const { isDark, mode } = useThemeState();
  const colors = useThemedColors();

  const [settings, setSettings] = useState({
    notifications: true,
  });

  const toggleSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const handleThemeChange = (newMode: 'light' | 'dark' | 'system') => {
    setThemeMode(newMode);
  };



  const handleResetApp = () => {
    Alert.alert(
      'Uygulamayı Sıfırla',
      'Bu işlem tüm verilerinizi silecektir. Bu işlem geri alınamaz!',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Sıfırla', style: 'destructive', onPress: () => {
          Alert.alert('Sıfırlandı', 'Uygulama başarıyla sıfırlandı');
        }}
      ]
    );
  };

  const styles = useThemedStyles(createStyles);

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor={styles.container.backgroundColor} />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>


        {/* Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Bildirimler</Text>
          <View style={styles.sectionContent}>
            <SettingItem
              icon="notifications-outline"
              title="Push Bildirimleri"
              subtitle="Harcama ve gelir bildirimleri"
              showArrow={false}
              styles={styles}
              rightComponent={
                <Switch
                  value={settings.notifications}
                  onValueChange={() => toggleSetting('notifications')}
                  trackColor={{ false: styles.switchTrack.backgroundColor, true: styles.switchTrackActive.backgroundColor }}
                  thumbColor={settings.notifications ? styles.switchThumbActive.backgroundColor : styles.switchThumb.backgroundColor}
                />
              }
            />
          </View>
        </View>

        {/* Appearance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Görünüm</Text>
          <View style={styles.sectionContent}>
            <SettingItem
              icon="moon-outline"
              title="Karanlık Mod"
              subtitle={mode === 'system' ? 'Sistem ayarını takip eder' : (isDark ? 'Karanlık tema aktif' : 'Açık tema aktif')}
              styles={styles}
              onPress={() => {
                Alert.alert(
                  'Tema Seçimi',
                  'Hangi temayı kullanmak istiyorsunuz?',
                  [
                    { text: 'Açık Tema', onPress: () => handleThemeChange('light') },
                    { text: 'Karanlık Tema', onPress: () => handleThemeChange('dark') },
                    { text: 'Sistem Ayarı', onPress: () => handleThemeChange('system') },
                    { text: 'İptal', style: 'cancel' }
                  ]
                );
              }}
            />
            <SettingItem
              icon="globe-outline"
              title="Dil"
              subtitle="Türkçe"
              styles={styles}
              onPress={() => {
                Alert.alert('Yakında', 'Bu özellik yakında eklenecek');
              }}
            />
          </View>
        </View>

        {/* Danger Zone */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tehlikeli İşlemler</Text>
          <View style={styles.sectionContent}>
            <SettingItem
              icon="trash-outline"
              title="Uygulamayı Sıfırla"
              subtitle="Tüm verileri sil"
              styles={styles}
              onPress={handleResetApp}
            />
          </View>
        </View>

        {/* App Info */}
        <View style={styles.appInfo}>
          <Text style={styles.appInfoText}>Bütçe360 v1.0.0</Text>
          <Text style={styles.appInfoText}>© 2024 Nocy Tech</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  content: {
    flex: 1,
  },
  section: {
    marginTop: spacing.lg,
  },
  sectionTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionContent: {
    backgroundColor: colors.background.secondary,
    marginHorizontal: spacing.lg,
    borderRadius: spacing.md,
    overflow: 'hidden',
  },
  // Switch styles
  switchTrack: {
    backgroundColor: colors.border.primary,
  },
  switchTrackActive: {
    backgroundColor: colors.primary[200],
  },
  switchThumb: {
    backgroundColor: colors.surface.primary,
  },
  switchThumbActive: {
    backgroundColor: colors.primary[500],
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIconContainer: {
    width: 24,
    height: 24,
    marginRight: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  settingTextContainer: {
    flex: 1,
  },
  settingTitle: {
    ...typography.styles.body1,
    color: colors.text.primary,
    fontWeight: '500',
  },
  settingSubtitle: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingArrow: {
    fontSize: 20,
    color: colors.text.tertiary,
    marginLeft: spacing.sm,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  appInfoText: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
    marginBottom: spacing.xs,
  },
});

export default SettingsScreen;