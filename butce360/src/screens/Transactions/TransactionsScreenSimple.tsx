import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { useNavigation } from '../../navigation/AppNavigator';

const TransactionsScreen: React.FC = () => {
  const { state: authState } = useAuth();
  const { navigate } = useNavigation();

  // Guest state
  if (authState.isGuest) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.greeting}>İşlemlerim</Text>
          <Text style={styles.date}>Gelir ve gider takibi</Text>
        </View>

        <View style={styles.guestContainer}>
          <Text style={styles.guestIcon}>📝</Text>
          <Text style={styles.guestTitle}>İşlemlerinizi Görüntüle</Text>
          <Text style={styles.guestText}>
            G<PERSON>r ve gider takibi için giriş yapmanız gerekiyor.
          </Text>
          <TouchableOpacity 
            style={styles.loginButton} 
            onPress={() => navigate('Login')}
          >
            <Text style={styles.loginButtonText}><PERSON><PERSON><PERSON></Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Authenticated user - empty state for now
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.greeting}>İşlemlerim</Text>
        <Text style={styles.date}>Gelir ve gider takibi</Text>
        <TouchableOpacity style={styles.addButton}>
          <Text style={styles.addButtonText}>+ Yeni İşlem</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.emptyContainer}>
        <Text style={styles.emptyIcon}>📝</Text>
        <Text style={styles.emptyTitle}>Henüz işlem yok</Text>
        <Text style={styles.emptyText}>
          İlk gelir veya giderinizi ekleyerek başlayın
        </Text>
        <TouchableOpacity style={styles.addButton}>
          <Text style={styles.addButtonText}>İşlem Ekle</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    backgroundColor: '#0ea5e9',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 30,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  date: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.9,
    marginBottom: 20,
  },
  addButton: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  addButtonText: {
    color: '#0ea5e9',
    fontSize: 14,
    fontWeight: '600',
  },
  
  // Guest State
  guestContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  guestIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  guestTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },
  guestText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
    lineHeight: 24,
  },
  loginButton: {
    backgroundColor: '#0ea5e9',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
  },
  loginButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  
  // Empty State
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
    lineHeight: 24,
  },
});

export default TransactionsScreen;
