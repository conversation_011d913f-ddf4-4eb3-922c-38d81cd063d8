import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  Alert,
} from 'react-native';
import { useNavigation } from '../../navigation/AppNavigator';
import { useAuth } from '../../hooks/useAuth';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import { NumberFormatter } from '../../utils/number';
import { DateFormatter } from '../../utils/date';

// Mock transactions data
const mockTransactions = [
  {
    id: '1',
    title: 'Market Alışverişi',
    type: 'expense' as const,
    amount: 150.50,
    currency: 'TRY',
    categoryId: '1',
    categoryName: 'Yiyecek & İçecek',
    categoryColor: '#ff6b6b',
    categoryIcon: '🍕',
    transactionDate: new Date(),
    accountName: 'Ana Hesap',
    note: 'Haftalık market alışverişi',
  },
  {
    id: '2',
    title: 'Maaş',
    type: 'income' as const,
    amount: 8500.00,
    currency: 'TRY',
    categoryId: '2',
    categoryName: 'Maaş',
    categoryColor: '#2ecc71',
    categoryIcon: '💰',
    transactionDate: new Date(Date.now() - ********), // Yesterday
    accountName: 'Ana Hesap',
    note: 'Aylık maaş ödemesi',
  },
  {
    id: '3',
    title: 'Benzin',
    type: 'expense' as const,
    amount: 200.00,
    currency: 'TRY',
    categoryId: '3',
    categoryName: 'Ulaşım',
    categoryColor: '#4ecdc4',
    categoryIcon: '🚗',
    transactionDate: new Date(Date.now() - *********), // 2 days ago
    accountName: 'Kredi Kartı',
    note: 'Araç yakıt gideri',
  },
  {
    id: '4',
    title: 'Freelance Proje',
    type: 'income' as const,
    amount: 2500.00,
    currency: 'TRY',
    categoryId: '4',
    categoryName: 'Freelance',
    categoryColor: '#3498db',
    categoryIcon: '💻',
    transactionDate: new Date(Date.now() - *********), // 3 days ago
    accountName: 'Ana Hesap',
    note: 'Web sitesi geliştirme projesi',
  },
  {
    id: '5',
    title: 'Restoran',
    type: 'expense' as const,
    amount: 85.00,
    currency: 'TRY',
    categoryId: '5',
    categoryName: 'Eğlence',
    categoryColor: '#96ceb4',
    categoryIcon: '🎬',
    transactionDate: new Date(Date.now() - *********), // 4 days ago
    accountName: 'Kredi Kartı',
    note: 'Arkadaşlarla akşam yemeği',
  },
];

const TransactionListScreen: React.FC = () => {
  const { navigate, goBack } = useNavigation();
  const { state: authState } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'income' | 'expense'>('all');

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleAddTransaction = () => {
    if (authState.isGuest) {
      Alert.alert('Giriş Gerekli', 'Bu özelliği kullanmak için giriş yapmanız gerekiyor.');
      return;
    }
    navigate('AddTransaction');
  };

  const handleTransactionPress = (transactionId: string) => {
    navigate('TransactionDetails', { transactionId });
  };

  const filteredTransactions = mockTransactions.filter(transaction => {
    if (selectedFilter === 'all') return true;
    return transaction.type === selectedFilter;
  });

  const renderFilterButtons = () => (
    <View style={styles.filterContainer}>
      {[
        { key: 'all', label: 'Tümü', count: mockTransactions.length },
        { key: 'income', label: 'Gelir', count: mockTransactions.filter(t => t.type === 'income').length },
        { key: 'expense', label: 'Gider', count: mockTransactions.filter(t => t.type === 'expense').length },
      ].map((filter) => (
        <TouchableOpacity
          key={filter.key}
          style={[
            styles.filterButton,
            selectedFilter === filter.key && styles.filterButtonActive
          ]}
          onPress={() => setSelectedFilter(filter.key as any)}
        >
          <Text style={[
            styles.filterButtonText,
            selectedFilter === filter.key && styles.filterButtonTextActive
          ]}>
            {filter.label} ({filter.count})
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderTransactionItem = (transaction: typeof mockTransactions[0]) => (
    <TouchableOpacity
      key={transaction.id}
      style={styles.transactionItem}
      onPress={() => handleTransactionPress(transaction.id)}
    >
      <View style={styles.transactionLeft}>
        <View style={[
          styles.categoryIcon,
          { backgroundColor: transaction.categoryColor }
        ]}>
          <Text style={styles.categoryIconText}>
            {transaction.categoryIcon}
          </Text>
        </View>
        <View style={styles.transactionInfo}>
          <Text style={styles.transactionTitle}>{transaction.title}</Text>
          <Text style={styles.transactionCategory}>
            {transaction.categoryName} • {transaction.accountName}
          </Text>
          <Text style={styles.transactionDate}>
            {DateFormatter.getRelativeDateString(transaction.transactionDate)}
          </Text>
          {transaction.note && (
            <Text style={styles.transactionNote} numberOfLines={1}>
              {transaction.note}
            </Text>
          )}
        </View>
      </View>
      <View style={styles.transactionRight}>
        <Text style={[
          styles.transactionAmount,
          transaction.type === 'income' ? styles.incomeAmount : styles.expenseAmount
        ]}>
          {transaction.type === 'expense' ? '-' : '+'}
          {NumberFormatter.formatCurrency(transaction.amount, transaction.currency)}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>📝</Text>
      <Text style={styles.emptyStateTitle}>
        {selectedFilter === 'all' ? 'Henüz işlem yok' : 
         selectedFilter === 'income' ? 'Henüz gelir yok' : 'Henüz gider yok'}
      </Text>
      <Text style={styles.emptyStateText}>
        {selectedFilter === 'all' ? 'İlk gelir veya giderinizi ekleyerek başlayın' :
         selectedFilter === 'income' ? 'İlk gelirinizi ekleyerek başlayın' : 'İlk giderinizi ekleyerek başlayın'}
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddTransaction}>
        <Text style={styles.addButtonText}>+ İşlem Ekle</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={goBack}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.title}>İşlemler</Text>
        <TouchableOpacity style={styles.addHeaderButton} onPress={handleAddTransaction}>
          <Text style={styles.addHeaderButtonText}>+</Text>
        </TouchableOpacity>
      </View>

      {/* Filter Buttons */}
      {renderFilterButtons()}

      {/* Transaction List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {filteredTransactions.length > 0 ? (
          filteredTransactions.map(renderTransactionItem)
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  
  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: colors.text.primary,
  },
  title: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  addHeaderButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary[500],
    borderRadius: 20,
  },
  addHeaderButtonText: {
    fontSize: 20,
    color: colors.text.inverse,
    fontWeight: 'bold',
  },

  // Filter
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.lg,
    gap: spacing.md,
  },
  filterButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: spacing.cardRadius,
    backgroundColor: colors.background.secondary,
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: colors.primary[500],
  },
  filterButtonText: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    fontWeight: '600',
  },
  filterButtonTextActive: {
    color: colors.text.inverse,
  },

  // List
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.screenPadding,
    paddingBottom: spacing['4xl'],
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.surface.primary,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  categoryIconText: {
    fontSize: 18,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    ...typography.styles.body1,
    color: colors.text.primary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  transactionCategory: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  transactionDate: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
    marginBottom: spacing.xs,
  },
  transactionNote: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
    fontStyle: 'italic',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    ...typography.styles.currencySmall,
    fontWeight: '700',
  },
  incomeAmount: {
    color: colors.secondary[600],
  },
  expenseAmount: {
    color: colors.accent[600],
  },

  // Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing['6xl'],
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: spacing.lg,
  },
  emptyStateTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptyStateText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    maxWidth: 250,
    marginBottom: spacing.xl,
  },
  addButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: spacing.cardRadius,
  },
  addButtonText: {
    ...typography.styles.button,
    color: colors.text.inverse,
    textTransform: 'none',
  },
});

export default TransactionListScreen;
