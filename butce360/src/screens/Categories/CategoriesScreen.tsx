import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  Alert,
} from 'react-native';

import { useAuth } from '../../hooks/useAuth';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';

// Mock categories data
const mockCategories = {
  income: [
    { id: '1', name: '<PERSON><PERSON><PERSON>', color: '#2ecc71', icon: '💰', isDefault: true, transactionCount: 12 },
    { id: '2', name: 'Freelance', color: '#3498db', icon: '💻', isDefault: false, transactionCount: 5 },
    { id: '3', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', color: '#9b59b6', icon: '📈', isDefault: false, transactionCount: 3 },
    { id: '4', name: '<PERSON><PERSON><PERSON>', color: '#e74c3c', icon: '🎁', isDefault: false, transactionCount: 2 },
    { id: '5', name: '<PERSON><PERSON><PERSON>', color: '#f39c12', icon: '💵', isDefault: true, transactionCount: 1 },
  ],
  expense: [
    { id: '6', name: 'Yiyecek & İçecek', color: '#ff6b6b', icon: '🍕', isDefault: true, transactionCount: 25 },
    { id: '7', name: 'Ulaşım', color: '#4ecdc4', icon: '🚗', isDefault: true, transactionCount: 18 },
    { id: '8', name: 'Alışveriş', color: '#45b7d1', icon: '🛍️', isDefault: true, transactionCount: 15 },
    { id: '9', name: 'Eğlence', color: '#96ceb4', icon: '🎬', isDefault: true, transactionCount: 12 },
    { id: '10', name: 'Faturalar', color: '#ffeaa7', icon: '📄', isDefault: true, transactionCount: 8 },
    { id: '11', name: 'Sağlık', color: '#dda0dd', icon: '🏥', isDefault: true, transactionCount: 4 },
    { id: '12', name: 'Eğitim', color: '#98d8c8', icon: '📚', isDefault: true, transactionCount: 3 },
    { id: '13', name: 'Seyahat', color: '#f7dc6f', icon: '✈️', isDefault: true, transactionCount: 2 },
  ],
};

interface CategoriesScreenProps {
  onNavigate?: (screen: string) => void;
}

const CategoriesScreen: React.FC<CategoriesScreenProps> = ({ onNavigate }) => {
  const { state: authState } = useAuth();
  const themedColors = useThemedColors();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'income' | 'expense'>('expense');

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleAddCategory = () => {
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Kategori eklemek için giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => onNavigate && onNavigate('login') },
        ]
      );
      return;
    }
    onNavigate && onNavigate('AddCategory');
  };

  const handleCategoryPress = (_categoryId: string) => {
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Kategori düzenlemek için giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => onNavigate && onNavigate('login') },
        ]
      );
      return;
    }
    onNavigate && onNavigate('EditCategory');
  };

  const handleDeleteCategory = (categoryId: string, categoryName: string, isDefault: boolean) => {
    if (isDefault) {
      Alert.alert(
        'Varsayılan Kategori',
        'Varsayılan kategoriler silinemez.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    Alert.alert(
      'Kategoriyi Sil',
      `"${categoryName}" kategorisini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => {
            // TODO: Implement delete category
            Alert.alert('Başarılı', 'Kategori silindi.');
          }
        },
      ]
    );
  };

  const currentCategories = mockCategories[selectedTab];

  const renderTabButtons = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[
          styles.tabButton,
          { backgroundColor: themedColors.background.secondary },
          selectedTab === 'income' && [styles.tabButtonActive, { backgroundColor: themedColors.primary[500] }]
        ]}
        onPress={() => setSelectedTab('income')}
      >
        <Text style={[
          styles.tabButtonText,
          { color: themedColors.text.secondary },
          selectedTab === 'income' && [styles.tabButtonTextActive, { color: themedColors.background.secondary }]
        ]}>
          💰 Gelir ({mockCategories.income.length})
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.tabButton,
          { backgroundColor: themedColors.background.secondary },
          selectedTab === 'expense' && [styles.tabButtonActive, { backgroundColor: themedColors.primary[500] }]
        ]}
        onPress={() => setSelectedTab('expense')}
      >
        <Text style={[
          styles.tabButtonText,
          { color: themedColors.text.secondary },
          selectedTab === 'expense' && [styles.tabButtonTextActive, { color: themedColors.background.secondary }]
        ]}>
          💸 Gider ({mockCategories.expense.length})
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderCategoryItem = (category: typeof currentCategories[0]) => (
    <TouchableOpacity
      key={category.id}
      style={[styles.categoryItem, { backgroundColor: themedColors.background.secondary }]}
      onPress={() => handleCategoryPress(category.id)}
      activeOpacity={0.7}
    >
      <View style={styles.categoryLeft}>
        <View style={[
          styles.categoryIcon,
          { backgroundColor: category.color }
        ]}>
          <Text style={styles.categoryIconText}>
            {category.icon}
          </Text>
        </View>
        <View style={styles.categoryInfo}>
          <View style={styles.categoryHeader}>
            <Text style={[styles.categoryName, { color: themedColors.text.primary }]}>{category.name}</Text>
            {category.isDefault && (
              <View style={[styles.defaultBadge, { backgroundColor: themedColors.primary[100] }]}>
                <Text style={[styles.defaultBadgeText, { color: themedColors.primary[700] }]}>Varsayılan</Text>
              </View>
            )}
          </View>
          <Text style={[styles.categoryStats, { color: themedColors.text.secondary }]}>
            {category.transactionCount} işlem
          </Text>
        </View>
      </View>
      <View style={styles.categoryRight}>
        <TouchableOpacity
          style={styles.moreButton}
          onPress={() => {
            Alert.alert(
              category.name,
              'Ne yapmak istiyorsunuz?',
              [
                { text: 'İptal', style: 'cancel' },
                { text: 'Düzenle', onPress: () => handleCategoryPress(category.id) },
                ...(category.isDefault ? [] : [
                  {
                    text: 'Sil',
                    style: 'destructive' as const,
                    onPress: () => handleDeleteCategory(category.id, category.name, category.isDefault)
                  }
                ]),
              ]
            );
          }}
          activeOpacity={0.7}
        >
          <Text style={[styles.moreButtonText, { color: themedColors.text.secondary }]}>⋯</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>
        {selectedTab === 'income' ? '💰' : '💸'}
      </Text>
      <Text style={[styles.emptyStateTitle, { color: themedColors.text.primary }]}>
        {selectedTab === 'income' ? 'Henüz gelir kategorisi yok' : 'Henüz gider kategorisi yok'}
      </Text>
      <Text style={[styles.emptyStateText, { color: themedColors.text.secondary }]}>
        {selectedTab === 'income'
          ? 'İlk gelir kategorinizi ekleyerek başlayın'
          : 'İlk gider kategorinizi ekleyerek başlayın'}
      </Text>
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: themedColors.primary[500] }]}
        onPress={handleAddCategory}
        activeOpacity={0.8}
      >
        <Text style={[styles.addButtonText, { color: themedColors.background.secondary }]}>+ Kategori Ekle</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: themedColors.background.primary }]}>
      <StatusBar
        barStyle={themedColors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={themedColors.background.primary}
      />

      {/* Tab Buttons */}
      {renderTabButtons()}

      {/* Category List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[themedColors.primary[500]]}
            tintColor={themedColors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {currentCategories.length > 0 ? (
          currentCategories.map(renderCategoryItem)
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Tabs
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 12,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    minHeight: 44,
  },
  tabButtonActive: {},
  tabButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
    fontWeight: '600',
  },
  tabButtonTextActive: {},

  // List
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 80,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  categoryIconText: {
    fontSize: 18,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryName: {
    ...typography.styles.listItem,
    fontWeight: '600',
    marginRight: 8,
  },
  defaultBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  defaultBadgeText: {
    ...typography.styles.caption2,
    fontSize: 10,
  },
  categoryStats: {
    ...typography.styles.footnote,
  },
  categoryRight: {
    alignItems: 'flex-end',
  },
  moreButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreButtonText: {
    fontSize: 20,
  },

  // Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    ...typography.styles.title3,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    ...typography.styles.subhead,
    textAlign: 'center',
    maxWidth: 250,
    marginBottom: 32,
  },
  addButton: {
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    minHeight: 44,
  },
  addButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
  },
});

export default CategoriesScreen;
