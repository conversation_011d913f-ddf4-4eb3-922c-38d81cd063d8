import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '../../navigation/AppNavigator';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import { NumberFormatter } from '../../utils/number';

const mockCategoryData = [
  { name: 'Yiyecek & İçecek', amount: 2150.50, percentage: 31.8, color: '#ff6b6b', icon: '🍕', transactions: 23 },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', amount: 1200.00, percentage: 17.8, color: '#4ecdc4', icon: '🚗', transactions: 15 },
  { name: 'Alışveriş', amount: 950.75, percentage: 14.1, color: '#45b7d1', icon: '🛍️', transactions: 12 },
  { name: 'Eğlence', amount: 800.00, percentage: 11.8, color: '#96ceb4', icon: '🎬', transactions: 8 },
  { name: '<PERSON><PERSON><PERSON>', amount: 650.00, percentage: 9.6, color: '#ffeaa7', icon: '📄', transactions: 6 },
  { name: 'Diğer', amount: 999.00, percentage: 14.9, color: '#95a5a6', icon: '📦', transactions: 18 },
];

const CategoryReportScreen: React.FC = () => {
  const { goBack } = useNavigation();

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={goBack} style={styles.backButton}>
        <Text style={styles.backButtonText}>←</Text>
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Kategori Dağılımı</Text>
      <View style={styles.placeholder} />
    </View>
  );

  const renderCategoryItem = (category: typeof mockCategoryData[0], index: number) => (
    <View key={index} style={styles.categoryItem}>
      <View style={styles.categoryLeft}>
        <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
          <Text style={styles.categoryIconText}>{category.icon}</Text>
        </View>
        <View style={styles.categoryInfo}>
          <Text style={styles.categoryName}>{category.name}</Text>
          <Text style={styles.categoryTransactions}>{category.transactions} işlem</Text>
        </View>
      </View>
      <View style={styles.categoryRight}>
        <Text style={styles.categoryAmount}>
          {NumberFormatter.formatCurrency(category.amount)}
        </Text>
        <Text style={styles.categoryPercentage}>{category.percentage}%</Text>
      </View>
    </View>
  );

  const totalAmount = mockCategoryData.reduce((sum, category) => sum + category.amount, 0);

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ScrollView style={styles.content}>
        <View style={styles.summary}>
          <Text style={styles.summaryTitle}>Toplam Harcama</Text>
          <Text style={styles.summaryAmount}>
            {NumberFormatter.formatCurrency(totalAmount)}
          </Text>
        </View>

        <View style={styles.categoryList}>
          {mockCategoryData.map((category, index) => renderCategoryItem(category, index))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: spacing.xs,
  },
  backButtonText: {
    fontSize: 24,
    color: colors.primary[500],
  },
  headerTitle: {
    ...typography.styles.h2,
    color: colors.text.primary,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  summary: {
    backgroundColor: colors.surface.primary,
    padding: spacing.md,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  summaryTitle: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  summaryAmount: {
    ...typography.styles.h1,
    color: colors.text.primary,
  },
  categoryList: {
    backgroundColor: colors.surface.primary,
    borderRadius: 12,
    padding: spacing.sm,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  categoryIconText: {
    fontSize: 18,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    ...typography.styles.body1,
    color: colors.text.primary,
    marginBottom: 2,
  },
  categoryTransactions: {
    ...typography.styles.caption,
    color: colors.text.secondary,
  },
  categoryRight: {
    alignItems: 'flex-end',
  },
  categoryAmount: {
    ...typography.styles.body1,
    color: colors.text.primary,
    fontWeight: '600',
    marginBottom: 2,
  },
  categoryPercentage: {
    ...typography.styles.caption,
    color: colors.text.secondary,
  },
});

export default CategoryReportScreen;
