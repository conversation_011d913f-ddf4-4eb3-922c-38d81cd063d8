import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '../../navigation/AppNavigator';
import { colors } from '../../theme/colors';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import { NumberFormatter } from '../../utils/number';

// const { width } = Dimensions.get('window');

const mockMonthlyData = [
  { month: 'Ocak', income: 7500, expense: 4200, net: 3300 },
  { month: 'Şubat', income: 8200, expense: 4800, net: 3400 },
  { month: 'Mart', income: 8500, expense: 5200, net: 3300 },
  { month: 'Nisan', income: 9200, expense: 5800, net: 3400 },
  { month: 'Mayıs', income: 11000, expense: 6750, net: 4250 },
  { month: 'Haziran', income: 10500, expense: 6200, net: 4300 },
];

const MonthlyReportScreen: React.FC = () => {
  const { goBack } = useNavigation();

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={goBack} style={styles.backButton}>
        <Text style={styles.backButtonText}>←</Text>
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Aylık Trend</Text>
      <View style={styles.placeholder} />
    </View>
  );

  const renderChart = () => (
    <View style={styles.chartContainer}>
      <Text style={styles.chartTitle}>Gelir vs Gider Trendi</Text>
      <View style={styles.chartPlaceholder}>
        <Text style={styles.chartPlaceholderText}>
          📊 Grafik Görselleştirmesi
        </Text>
        <Text style={styles.chartPlaceholderSubtext}>
          Gelir ve gider trendlerini gösteren interaktif grafik burada yer alacak
        </Text>
      </View>
    </View>
  );

  const renderMonthlyItem = (item: typeof mockMonthlyData[0], index: number) => {
    const incomeChange = index > 0 ? 
      ((item.income - mockMonthlyData[index - 1].income) / mockMonthlyData[index - 1].income) * 100 : 0;
    const expenseChange = index > 0 ? 
      ((item.expense - mockMonthlyData[index - 1].expense) / mockMonthlyData[index - 1].expense) * 100 : 0;

    return (
      <View key={index} style={styles.monthlyItem}>
        <View style={styles.monthHeader}>
          <Text style={styles.monthName}>{item.month}</Text>
          <Text style={[
            styles.netAmount,
            item.net >= 0 ? styles.positiveAmount : styles.negativeAmount
          ]}>
            {NumberFormatter.formatCurrency(item.net)}
          </Text>
        </View>
        
        <View style={styles.monthDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Gelir</Text>
            <View style={styles.detailRight}>
              <Text style={styles.detailAmount}>
                {NumberFormatter.formatCurrency(item.income)}
              </Text>
              {index > 0 && (
                <Text style={[
                  styles.changeText,
                  incomeChange >= 0 ? styles.positiveChange : styles.negativeChange
                ]}>
                  {incomeChange >= 0 ? '+' : ''}{incomeChange.toFixed(1)}%
                </Text>
              )}
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Gider</Text>
            <View style={styles.detailRight}>
              <Text style={styles.detailAmount}>
                {NumberFormatter.formatCurrency(item.expense)}
              </Text>
              {index > 0 && (
                <Text style={[
                  styles.changeText,
                  expenseChange >= 0 ? styles.negativeChange : styles.positiveChange
                ]}>
                  {expenseChange >= 0 ? '+' : ''}{expenseChange.toFixed(1)}%
                </Text>
              )}
            </View>
          </View>
        </View>
      </View>
    );
  };

  const totalIncome = mockMonthlyData.reduce((sum, item) => sum + item.income, 0);
  const totalExpense = mockMonthlyData.reduce((sum, item) => sum + item.expense, 0);
  const totalNet = totalIncome - totalExpense;

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ScrollView style={styles.content}>
        <View style={styles.summary}>
          <Text style={styles.summaryTitle}>6 Aylık Özet</Text>
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Toplam Gelir</Text>
              <Text style={[styles.summaryAmount, styles.incomeAmount]}>
                {NumberFormatter.formatCurrency(totalIncome)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Toplam Gider</Text>
              <Text style={[styles.summaryAmount, styles.expenseAmount]}>
                {NumberFormatter.formatCurrency(totalExpense)}
              </Text>
            </View>
          </View>
          <View style={styles.netSummary}>
            <Text style={styles.summaryLabel}>Net Gelir</Text>
            <Text style={[
              styles.summaryAmount,
              totalNet >= 0 ? styles.incomeAmount : styles.expenseAmount
            ]}>
              {NumberFormatter.formatCurrency(totalNet)}
            </Text>
          </View>
        </View>

        {renderChart()}

        <View style={styles.monthlyList}>
          <Text style={styles.listTitle}>Aylık Detaylar</Text>
          {mockMonthlyData.map((item, index) => renderMonthlyItem(item, index))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: spacing.xs,
  },
  backButtonText: {
    fontSize: 24,
    color: colors.primary[500],
  },
  headerTitle: {
    ...typography.styles.h2,
    color: colors.text.primary,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
    padding: spacing.md,
  },
  summary: {
    backgroundColor: colors.surface.primary,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.md,
  },
  summaryTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  summaryAmount: {
    ...typography.styles.h4,
    fontWeight: '600',
  },
  incomeAmount: {
    color: colors.success[500],
  },
  expenseAmount: {
    color: colors.error[500],
  },
  netSummary: {
    alignItems: 'center',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border.primary,
  },
  chartContainer: {
    backgroundColor: colors.surface.primary,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.md,
  },
  chartTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  chartPlaceholder: {
    height: 200,
    backgroundColor: colors.background.secondary,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.md,
  },
  chartPlaceholderText: {
    ...typography.styles.h4,
    color: colors.text.secondary,
    marginBottom: spacing.sm,
  },
  chartPlaceholderSubtext: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  monthlyList: {
    backgroundColor: colors.surface.primary,
    borderRadius: 12,
    padding: spacing.sm,
  },
  listTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  monthlyItem: {
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  monthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  monthName: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  netAmount: {
    ...typography.styles.h4,
    fontWeight: '600',
  },
  positiveAmount: {
    color: colors.success[500],
  },
  negativeAmount: {
    color: colors.error[500],
  },
  monthDetails: {
    paddingLeft: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailLabel: {
    ...typography.styles.body2,
    color: colors.text.secondary,
  },
  detailRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailAmount: {
    ...typography.styles.body1,
    color: colors.text.primary,
    marginRight: spacing.sm,
  },
  changeText: {
    ...typography.styles.caption,
    fontWeight: '600',
  },
  positiveChange: {
    color: colors.success[500],
  },
  negativeChange: {
    color: colors.error[500],
  },
});

export default MonthlyReportScreen;
