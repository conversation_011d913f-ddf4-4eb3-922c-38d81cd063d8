import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Alert,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';

interface MenuItemProps {
  icon: string; // artık string tipi
  title: string;
  onPress: () => void;
  colors: any;
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, title, onPress, colors }) => (
  <TouchableOpacity
    style={[styles.menuItem, { backgroundColor: colors.background.secondary, borderBottomColor: colors.border.primary }]}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <View style={styles.menuItemLeft}>
      <Ionicons
        name={icon}
        size={22}
        color={colors.primary[500]}
        style={styles.menuIcon}
      />
      <Text style={[styles.menuTitle, { color: colors.text.primary }]}>{title}</Text>
    </View>
    <Ionicons name="chevron-forward" size={16} color={colors.text.secondary} />
  </TouchableOpacity>
);

interface MenuSectionProps {
  title: string;
  children: React.ReactNode;
  colors: any;
}

interface MenuScreenProps {
  onNavigate?: (screen: string) => void;
}

const MenuSection: React.FC<MenuSectionProps> = ({ title, children, colors }) => (
  <View style={styles.section}>
    <Text style={[styles.sectionTitle, { color: colors.text.secondary }]}>{title}</Text>
    <View style={[styles.sectionContent, { backgroundColor: colors.background.secondary }]}>{children}</View>
  </View>
);

const MenuScreen: React.FC<MenuScreenProps> = ({ onNavigate }) => {
  const colors = useThemedColors();
  const insets = useSafeAreaInsets();

  const handleFeaturePress = (feature: string) => {
    const featureScreens: { [key: string]: string } = {
      'Bütçe': 'budget',
      'Kategoriler': 'categories',
      'Hesaplar': 'accounts',
      'Raporlar': 'reports',
      'PDF Okuma': 'bankStatement',
      'Dışarı Aktarma': 'export',
    };
    const screenName = featureScreens[feature];
    if (screenName && onNavigate) {
      onNavigate(screenName);
    } else {
      Alert.alert('Yakında', `${feature} özelliği yakında eklenecek!`);
    }
  };

  const handleAccountPress = (action: string) => {
    const accountScreens: { [key: string]: string } = {
      'Profil': 'profile',
      'Giriş Yap': 'login',
      'Kayıt Ol': 'register',
      'Senkronizasyon': 'sync',
    };
    const screenName = accountScreens[action];
    if (screenName && onNavigate) {
      onNavigate(screenName);
    } else {
      Alert.alert('Yakında', `${action} özelliği yakında eklenecek!`);
    }
  };

  const handleAppPress = (setting: string) => {
    const appScreens: { [key: string]: string } = {
      'Ayarlar': 'settings',
      'Tema': 'theme',
      'Bildirimler': 'notifications',
      'Yardım': 'help',
      'Hakkında': 'about',
    };
    const screenName = appScreens[setting];
    if (screenName && onNavigate) {
      onNavigate(screenName);
    } else {
      Alert.alert('Yakında', `${setting} özelliği yakında eklenecek!`);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top + 16 }]}>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>Menü</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Özellikler Section */}
        <MenuSection title="ÖZELLİKLER" colors={colors}>
          <MenuItem icon="wallet-outline" title="Bütçe" onPress={() => handleFeaturePress('Bütçe')} colors={colors} />
          <MenuItem icon="pricetags-outline" title="Kategoriler" onPress={() => handleFeaturePress('Kategoriler')} colors={colors} />
          <MenuItem icon="business-outline" title="Hesaplar" onPress={() => handleFeaturePress('Hesaplar')} colors={colors} />
          <MenuItem icon="stats-chart-outline" title="Raporlar" onPress={() => handleFeaturePress('Raporlar')} colors={colors} />
          <MenuItem icon="document-text-outline" title="Banka Ekstre PDF Okuma" onPress={() => handleFeaturePress('PDF Okuma')} colors={colors} />
          <MenuItem icon="cloud-upload-outline" title="Verileri Dışarı Aktarma" onPress={() => handleFeaturePress('Dışarı Aktarma')} colors={colors} />
        </MenuSection>

        {/* Hesap Section */}
        <MenuSection title="HESAP" colors={colors}>
          <MenuItem icon="person-outline" title="Profil" onPress={() => handleAccountPress('Profil')} colors={colors} />
          <MenuItem icon="log-in-outline" title="Giriş Yap" onPress={() => handleAccountPress('Giriş Yap')} colors={colors} />
          <MenuItem icon="person-add-outline" title="Kayıt Ol" onPress={() => handleAccountPress('Kayıt Ol')} colors={colors} />
        </MenuSection>

        {/* Uygulama Section */}
        <MenuSection title="UYGULAMA" colors={colors}>
          <MenuItem icon="settings-outline" title="Ayarlar" onPress={() => handleAppPress('Ayarlar')} colors={colors} />
          <MenuItem icon="help-circle-outline" title="Yardım" onPress={() => handleAppPress('Yardım')} colors={colors} />
          <MenuItem icon="information-circle-outline" title="Hakkında" onPress={() => handleAppPress('Hakkında')} colors={colors} />
        </MenuSection>

        <View style={{ height: insets.bottom + 20 }} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    alignItems: 'center',
  },
  headerTitle: {
    ...typography.styles.largeTitle,
    fontWeight: '700',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16
  },
  section: {
    marginBottom: 32
  },
  sectionTitle: {
    ...typography.styles.sectionHeader,
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  sectionContent: {
    borderRadius: 10,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 0.33,
    minHeight: 44,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    marginRight: 12,
    width: 28,
    textAlign: 'center',
  },
  menuTitle: {
    ...typography.styles.listItem,
  },
});

export default MenuScreen;
