import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Alert,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

import { useAuth } from '../../hooks/useAuth';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Logo from '../../components/common/Logo';

interface MenuItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress: () => void;
  showArrow?: boolean;
  danger?: boolean;
  styles: any;
}

const MenuItem: React.FC<MenuItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  showArrow = true,
  danger = false,
  styles,
}) => (
  <TouchableOpacity style={styles.menuItem} onPress={onPress}>
    <View style={styles.menuItemLeft}>
      <View style={styles.menuItemIconContainer}>
        <Ionicons
          name={icon}
          size={20}
          color={danger ? '#ef4444' : '#6b7280'}
        />
      </View>
      <View style={styles.menuItemInfo}>
        <Text style={[styles.menuItemTitle, danger && styles.dangerText]}>
          {title}
        </Text>
        {subtitle && <Text style={styles.menuItemSubtitle}>{subtitle}</Text>}
      </View>
    </View>
    {showArrow && <Ionicons name="chevron-forward" size={16} color="#9ca3af" />}
  </TouchableOpacity>
);

interface ProfileScreenProps {
  onNavigate?: (screen: string) => void;
}

const ProfileScreen: React.FC<ProfileScreenProps> = ({ onNavigate }) => {
  const { state: authState, logout } = useAuth();
  const themedColors = useThemedColors();
  const styles = createStyles(themedColors);

  const handleLogout = () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkış yapmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              onNavigate?.('Welcome');
            } catch (error) {
              Alert.alert('Hata', 'Çıkış yapılırken bir hata oluştu.');
            }
          },
        },
      ],
    );
  };

  const handleGuestLogin = () => {
    Alert.alert(
      'Giriş Yap',
      'Tüm özelliklerden yararlanmak için giriş yapmak istiyor musunuz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Giriş Yap',
          onPress: () => {
            // Direkt login sayfasına git, disableGuestMode gereksiz
            onNavigate?.('login');
          },
        },
      ],
    );
  };

  const renderUserInfo = () => {
    console.log('is_guest:', authState.isGuest);
    if (authState.isGuest) {
      return (
        <View style={styles.userCard}>
          <View style={styles.avatarContainer}>
            <Logo size={60} variant="circle" />
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>Misafir Kullanıcı</Text>
            <Text style={styles.userEmail}>Giriş yapmadınız</Text>
          </View>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={handleGuestLogin}
          >
            <Text style={styles.loginButtonText}>Giriş Yap</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.userCard}>
        <View style={styles.avatarContainer}>
          <Text style={styles.avatarText}>
            {authState.user?.name?.charAt(0).toUpperCase() || '👤'}
          </Text>
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{authState.user?.name}</Text>
          <Text style={styles.userEmail}>{authState.user?.email}</Text>
        </View>
      </View>
    );
  };

  const renderAccountSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Hesap</Text>
      <View style={styles.menuGroup}>
        <MenuItem
          icon="person-outline"
          title="Profil Bilgileri"
          subtitle="Ad, e-posta ve diğer bilgiler"
          onPress={() => {
            if (authState.isGuest) {
              handleGuestLogin();
            } else {
              onNavigate?.('EditProfile');
            }
          }}
          styles={styles}
        />
        <MenuItem
          icon="lock-closed-outline"
          title="Şifre Değiştir"
          subtitle="Hesap güvenliğinizi koruyun"
          onPress={() => {
            if (authState.isGuest) {
              handleGuestLogin();
            } else {
              onNavigate?.('ChangePassword');
            }
          }}
          styles={styles}
        />
      </View>
    </View>
  );

  const renderLogoutSection = () => (
    <View style={styles.section}>
      <View style={styles.menuGroup}>
        <MenuItem
          icon="log-out-outline"
          title={authState.isGuest ? 'Misafir Modundan Çık' : 'Çıkış Yap'}
          onPress={handleLogout}
          showArrow={false}
          danger={true}
          styles={styles}
        />
      </View>
    </View>
  );

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: themedColors.background.primary },
      ]}
    >
      <StatusBar
        barStyle={
          themedColors.background.primary === '#1c1c1e'
            ? 'light-content'
            : 'dark-content'
        }
        backgroundColor={themedColors.background.primary}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderUserInfo()}
        {!authState.isGuest ? (
          <>
            {renderAccountSection()}
            {renderLogoutSection()}
          </>
        ) : null}
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },

    // Content
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingHorizontal: 16,
      paddingBottom: 80,
    },

    // User Card
    userCard: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
      marginHorizontal: spacing.screenPadding,
      marginTop: spacing.lg,
      marginBottom: spacing.xl,
      padding: spacing.lg,
      borderRadius: spacing.cardRadius,
      shadowColor: colors.neutral[900],
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    avatarContainer: {
      marginRight: spacing.lg,
    },
    avatarText: {
      fontSize: 24,
      color: colors.primary[600],
    },
    userInfo: {
      flex: 1,
    },
    userName: {
      ...typography.styles.h5,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    userEmail: {
      ...typography.styles.body2,
      color: colors.text.secondary,
    },
    loginButton: {
      backgroundColor: colors.primary[500],
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.lg,
      borderRadius: spacing.cardRadius,
    },
    loginButtonText: {
      ...typography.styles.buttonSmall,
      color: colors.text.inverse,
      textTransform: 'none',
    },
    editButton: {
      backgroundColor: colors.background.secondary,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.lg,
      borderRadius: spacing.cardRadius,
    },
    editButtonText: {
      ...typography.styles.buttonSmall,
      color: colors.text.primary,
      textTransform: 'none',
    },

    // Sections
    section: {
      marginBottom: spacing.xl,
    },
    sectionTitle: {
      ...typography.styles.h6,
      color: colors.text.primary,
      paddingHorizontal: spacing.screenPadding,
      marginBottom: spacing.md,
    },
    menuGroup: {
      backgroundColor: colors.background.secondary,
      marginHorizontal: spacing.screenPadding,
      borderRadius: spacing.cardRadius,
      shadowColor: colors.neutral[900],
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },

    // Menu Items
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: spacing.lg,
      paddingHorizontal: spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    },
    menuItemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    menuItemIconContainer: {
      width: 24,
      height: 24,
      marginRight: spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
    },
    menuItemInfo: {
      flex: 1,
    },
    menuItemTitle: {
      ...typography.styles.body1,
      color: colors.text.primary,
      marginBottom: spacing.xs,
    },
    menuItemSubtitle: {
      ...typography.styles.caption,
      color: colors.text.secondary,
    },
    menuItemArrow: {
      fontSize: 18,
      color: colors.text.secondary,
    },
    dangerText: {
      color: colors.error[500],
    },

    // Guest mode styles
    guestContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: spacing.xl,
    },
    guestContent: {
      alignItems: 'center',
      maxWidth: 300,
    },
    guestIcon: {
      marginBottom: spacing.xl,
    },
    guestTitle: {
      ...typography.styles.h4,
      textAlign: 'center',
      marginBottom: spacing.md,
      fontWeight: '600',
    },
    guestMessage: {
      ...typography.styles.body2,
      textAlign: 'center',
      marginBottom: spacing.xl,
      lineHeight: 22,
    },
    guestLoginButton: {
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.xl,
      borderRadius: spacing.sm,
      minWidth: 120,
    },
    guestLoginButtonText: {
      ...typography.styles.button,
      fontWeight: '600',
    },
  });

export default ProfileScreen;
