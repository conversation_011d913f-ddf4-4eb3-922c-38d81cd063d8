import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  Alert,
} from 'react-native';

import { useAuth } from '../../hooks/useAuth';

import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import { NumberFormatter } from '../../utils/number';
// import { getAccountColor } from '../../theme/colors';

// Mock accounts data
const mockAccounts = [
  {
    id: '1',
    name: '<PERSON>',
    type: 'checking' as const,
    balance: 15750.50,
    currency: 'TRY',
    color: '#2ecc71',
    icon: '🏦',
    isDefault: true,
    transactionCount: 45,
    lastTransaction: new Date(),
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    type: 'credit' as const,
    balance: -2340.75,
    currency: 'TRY',
    color: '#e74c3c',
    icon: '💳',
    isDefault: false,
    transactionCount: 28,
    lastTransaction: new Date(Date.now() - ********),
  },
  {
    id: '3',
    name: 'Nakit',
    type: 'cash' as const,
    balance: 450.00,
    currency: 'TRY',
    color: '#f39c12',
    icon: '💵',
    isDefault: false,
    transactionCount: 12,
    lastTransaction: new Date(Date.now() - *********),
  },
  {
    id: '4',
    name: 'Tasarruf Hesabı',
    type: 'savings' as const,
    balance: 25000.00,
    currency: 'TRY',
    color: '#3498db',
    icon: '🏛️',
    isDefault: false,
    transactionCount: 8,
    lastTransaction: new Date(Date.now() - *********),
  },
  {
    id: '5',
    name: 'Yatırım Hesabı',
    type: 'investment' as const,
    balance: 12500.25,
    currency: 'TRY',
    color: '#9b59b6',
    icon: '📈',
    isDefault: false,
    transactionCount: 15,
    lastTransaction: new Date(Date.now() - *********),
  },
];

interface AccountsScreenProps {
  onNavigate?: (screen: string) => void;
}

const AccountsScreen: React.FC<AccountsScreenProps> = ({ onNavigate }) => {
  const { state: authState } = useAuth();
  const themedColors = useThemedColors();
  const styles = createStyles(themedColors);
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleAddAccount = () => {
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Hesap eklemek için giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => onNavigate?.('Login') },
        ]
      );
      return;
    }
    onNavigate?.('AddAccount');
  };

  const handleAccountPress = (_accountId: string) => {
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Hesap detaylarını görmek için giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => onNavigate?.('Login') },
        ]
      );
      return;
    }
    onNavigate?.('AccountDetails');
  };

  const handleDeleteAccount = (accountId: string, accountName: string, isDefault: boolean) => {
    if (isDefault) {
      Alert.alert(
        'Varsayılan Hesap',
        'Varsayılan hesap silinemez.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    Alert.alert(
      'Hesabı Sil',
      `"${accountName}" hesabını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => {
            // TODO: Implement delete account
            Alert.alert('Başarılı', 'Hesap silindi.');
          }
        },
      ]
    );
  };

  const getAccountTypeLabel = (type: string) => {
    switch (type) {
      case 'checking': return 'Vadesiz Hesap';
      case 'savings': return 'Tasarruf Hesabı';
      case 'credit': return 'Kredi Kartı';
      case 'cash': return 'Nakit';
      case 'investment': return 'Yatırım Hesabı';
      default: return 'Diğer';
    }
  };

  const totalBalance = mockAccounts.reduce((sum, account) => sum + account.balance, 0);

  const renderSummaryCard = () => (
    <View style={styles.summaryCard}>
      <Text style={styles.summaryTitle}>Toplam Bakiye</Text>
      <Text style={[
        styles.summaryAmount,
        totalBalance >= 0 ? styles.positiveAmount : styles.negativeAmount
      ]}>
        {NumberFormatter.formatCurrency(totalBalance, 'TRY')}
      </Text>
      <Text style={styles.summarySubtitle}>
        {mockAccounts.length} hesap
      </Text>
    </View>
  );

  const renderAccountItem = (account: typeof mockAccounts[0]) => (
    <TouchableOpacity
      key={account.id}
      style={styles.accountItem}
      onPress={() => handleAccountPress(account.id)}
    >
      <View style={styles.accountLeft}>
        <View style={[
          styles.accountIcon,
          { backgroundColor: account.color }
        ]}>
          <Text style={styles.accountIconText}>
            {account.icon}
          </Text>
        </View>
        <View style={styles.accountInfo}>
          <View style={styles.accountHeader}>
            <Text style={styles.accountName}>{account.name}</Text>
            {account.isDefault && (
              <View style={styles.defaultBadge}>
                <Text style={styles.defaultBadgeText}>Varsayılan</Text>
              </View>
            )}
          </View>
          <Text style={styles.accountType}>
            {getAccountTypeLabel(account.type)}
          </Text>
          <Text style={styles.accountStats}>
            {account.transactionCount} işlem
          </Text>
        </View>
      </View>
      <View style={styles.accountRight}>
        <Text style={[
          styles.accountBalance,
          account.balance >= 0 ? styles.positiveAmount : styles.negativeAmount
        ]}>
          {NumberFormatter.formatCurrency(account.balance, account.currency)}
        </Text>
        <TouchableOpacity
          style={styles.moreButton}
          onPress={() => {
            Alert.alert(
              account.name,
              'Ne yapmak istiyorsunuz?',
              [
                { text: 'İptal', style: 'cancel' },
                { text: 'Detaylar', onPress: () => handleAccountPress(account.id) },
                { text: 'Düzenle', onPress: () => onNavigate?.('EditAccount') },
                ...(account.isDefault ? [] : [
                  {
                    text: 'Sil',
                    style: 'destructive' as const,
                    onPress: () => handleDeleteAccount(account.id, account.name, account.isDefault)
                  }
                ]),
              ]
            );
          }}
        >
          <Text style={styles.moreButtonText}>⋯</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>🏦</Text>
      <Text style={styles.emptyStateTitle}>Henüz hesap yok</Text>
      <Text style={styles.emptyStateText}>
        İlk hesabınızı ekleyerek başlayın
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddAccount}>
        <Text style={styles.addButtonText}>+ Hesap Ekle</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={themedColors.background.primary} />
      
      {/* Account List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[themedColors.primary[500]]}
            tintColor={themedColors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {mockAccounts.length > 0 && renderSummaryCard()}
        
        {mockAccounts.length > 0 ? (
          mockAccounts.map(renderAccountItem)
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  


  // Summary Card
  summaryCard: {
    backgroundColor: colors.background.secondary, // Hesaplarla aynı arka plan
    marginHorizontal: spacing.screenPadding,
    marginBottom: spacing.xl,
    borderRadius: spacing.cardRadius,
    padding: spacing.cardPadding,
    alignItems: 'center',
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    ...typography.styles.body2,
    color: colors.text.secondary, // Normal text rengi
    marginBottom: spacing.sm,
  },
  summaryAmount: {
    ...typography.styles.currencyLarge,
    color: colors.text.primary, // Normal text rengi
    fontWeight: '700',
    marginBottom: spacing.xs,
  },
  summarySubtitle: {
    ...typography.styles.caption,
    color: colors.text.secondary, // Normal text rengi
    opacity: 0.8,
  },

  // List
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: spacing.lg, // Üstten padding
    paddingBottom: spacing['4xl'],
  },
  accountItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    marginHorizontal: spacing.screenPadding, // Toplam bakiye ile aynı hizada
    marginBottom: spacing.md,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  accountLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  accountIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  accountIconText: {
    fontSize: 20,
  },
  accountInfo: {
    flex: 1,
  },
  accountHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  accountName: {
    ...typography.styles.body1,
    color: colors.text.primary,
    fontWeight: '600',
    marginRight: spacing.sm,
  },
  defaultBadge: {
    backgroundColor: colors.primary[100],
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: spacing.xs,
  },
  defaultBadgeText: {
    ...typography.styles.overline,
    color: colors.primary[700],
    fontSize: 10,
  },
  accountType: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  accountStats: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
  },
  accountRight: {
    alignItems: 'flex-end',
  },
  accountBalance: {
    ...typography.styles.currencySmall,
    fontWeight: '700',
    marginBottom: spacing.sm,
  },
  positiveAmount: {
    color: colors.secondary[600],
  },
  negativeAmount: {
    color: colors.accent[600],
  },
  moreButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreButtonText: {
    fontSize: 20,
    color: colors.text.secondary,
  },

  // Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing['6xl'],
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: spacing.lg,
  },
  emptyStateTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptyStateText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    maxWidth: 250,
    marginBottom: spacing.xl,
  },
  addButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: spacing.cardRadius,
  },
  addButtonText: {
    ...typography.styles.button,
    color: colors.text.inverse,
    textTransform: 'none',
  },
});

export default AccountsScreen;
