// Number utility functions for currency and formatting

export class NumberFormatter {
  // Format currency with Turkish Lira
  static formatCurrency(
    amount: number | undefined | null,
    currency: string = 'TRY',
    showSymbol: boolean = true
  ): string {
    // Handle undefined/null values
    if (amount === undefined || amount === null || isNaN(amount)) {
      amount = 0;
    }

    // Normalize currency code
    let normalizedCurrency = currency;
    if (currency === 'TL' || currency === 'tl' || !currency) {
      normalizedCurrency = 'TRY';
    }

    try {
      const formatter = new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: normalizedCurrency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });

      if (!showSymbol) {
        return formatter.format(amount).replace(/[^\d,.-]/g, '').trim();
      }

      return formatter.format(amount);
    } catch (error) {
      // Fallback to simple formatting if currency is invalid
      console.warn('[NumberFormatter] Invalid currency:', currency, 'using fallback');
      return `${amount.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} TL`;
    }
  }

  // Format number with thousand separators
  static formatNumber(
    number: number,
    decimals: number = 2
  ): string {
    return new Intl.NumberFormat('tr-TR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(number);
  }

  // Parse currency string to number
  static parseCurrency(currencyString: string): number {
    // Remove currency symbols and spaces, replace comma with dot
    const cleanString = currencyString
      .replace(/[^\d,.-]/g, '')
      .replace(',', '.');

    const number = parseFloat(cleanString);
    return isNaN(number) ? 0 : number;
  }

  // Format percentage
  static formatPercentage(
    value: number,
    decimals: number = 1
  ): string {
    return new Intl.NumberFormat('tr-TR', {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value / 100);
  }

  // Round to specified decimal places
  static roundTo(number: number, decimals: number = 2): number {
    const factor = Math.pow(10, decimals);
    return Math.round(number * factor) / factor;
  }

  // Check if number is positive
  static isPositive(number: number): boolean {
    return number > 0;
  }

  // Check if number is negative
  static isNegative(number: number): boolean {
    return number < 0;
  }

  // Get absolute value
  static abs(number: number): number {
    return Math.abs(number);
  }

  // Format large numbers with K, M, B suffixes
  static formatLargeNumber(number: number): string {
    if (number >= 1000000000) {
      return NumberFormatter.roundTo(number / 1000000000, 1) + 'B';
    }
    if (number >= 1000000) {
      return NumberFormatter.roundTo(number / 1000000, 1) + 'M';
    }
    if (number >= 1000) {
      return NumberFormatter.roundTo(number / 1000, 1) + 'K';
    }
    return NumberFormatter.formatNumber(number, 0);
  }

  // Calculate percentage of total
  static calculatePercentage(value: number, total: number): number {
    if (total === 0) return 0;
    return NumberFormatter.roundTo((value / total) * 100, 1);
  }

  // Generate random ID
  static generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  // Validate if string is a valid number
  static isValidNumber(value: string): boolean {
    const number = NumberFormatter.parseCurrency(value);
    return !isNaN(number) && isFinite(number);
  }

  // Clamp number between min and max
  static clamp(number: number, min: number, max: number): number {
    return Math.min(Math.max(number, min), max);
  }

  // Sum array of numbers
  static sum(numbers: number[]): number {
    return numbers.reduce((total, num) => total + num, 0);
  }

  // Average of array of numbers
  static average(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return NumberFormatter.sum(numbers) / numbers.length;
  }

  // Find minimum in array
  static min(numbers: number[]): number {
    return Math.min(...numbers);
  }

  // Find maximum in array
  static max(numbers: number[]): number {
    return Math.max(...numbers);
  }
}