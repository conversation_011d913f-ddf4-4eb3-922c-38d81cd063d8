// Date utility functions for the budget app

export class DateFormatter {
  // Format date to readable string
  static formatDate(date: Date, format: 'short' | 'long' | 'medium' = 'medium'): string {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: format === 'short' ? 'numeric' : format === 'long' ? 'long' : 'short',
      day: 'numeric',
    };

    return date.toLocaleDateString('tr-TR', options);
  }

  // Format date for API (ISO string)
  static formatForApi(date: Date): string {
    return date.toISOString();
  }

  // Parse date from API response
  static parseFromApi(dateString: string): Date {
    return new Date(dateString);
  }

  // Get start of month
  static getStartOfMonth(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth(), 1);
  }

  // Get end of month
  static getEndOfMonth(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999);
  }

  // Get start of week (Monday)
  static getStartOfWeek(date: Date): Date {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    return new Date(date.setDate(diff));
  }

  // Get end of week (Sunday)
  static getEndOfWeek(date: Date): Date {
    const startOfWeek = DateFormatter.getStartOfWeek(new Date(date));
    return new Date(startOfWeek.getTime() + 6 * 24 * 60 * 60 * 1000);
  }

  // Get start of year
  static getStartOfYear(date: Date): Date {
    return new Date(date.getFullYear(), 0, 1);
  }

  // Get end of year
  static getEndOfYear(date: Date): Date {
    return new Date(date.getFullYear(), 11, 31, 23, 59, 59, 999);
  }

  // Check if two dates are the same day
  static isSameDay(date1: Date, date2: Date): boolean {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }

  // Check if date is today
  static isToday(date: Date): boolean {
    return DateFormatter.isSameDay(date, new Date());
  }

  // Check if date is yesterday
  static isYesterday(date: Date): boolean {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return DateFormatter.isSameDay(date, yesterday);
  }

  // Get relative date string (Today, Yesterday, or formatted date)
  static getRelativeDateString(date: Date): string {
    if (DateFormatter.isToday(date)) {
      return 'Bugün';
    }
    if (DateFormatter.isYesterday(date)) {
      return 'Dün';
    }
    return DateFormatter.formatDate(date, 'short');
  }

  // Get month name in Turkish
  static getMonthName(date: Date, format: 'short' | 'long' = 'long'): string {
    const options: Intl.DateTimeFormatOptions = {
      month: format === 'short' ? 'short' : 'long',
    };
    return date.toLocaleDateString('tr-TR', options);
  }

  // Get day name in Turkish
  static getDayName(date: Date, format: 'short' | 'long' = 'long'): string {
    const options: Intl.DateTimeFormatOptions = {
      weekday: format === 'short' ? 'short' : 'long',
    };
    return date.toLocaleDateString('tr-TR', options);
  }

  // Add days to date
  static addDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  // Add months to date
  static addMonths(date: Date, months: number): Date {
    const result = new Date(date);
    result.setMonth(result.getMonth() + months);
    return result;
  }

  // Add years to date
  static addYears(date: Date, years: number): Date {
    const result = new Date(date);
    result.setFullYear(result.getFullYear() + years);
    return result;
  }

  // Get difference in days
  static getDaysDifference(date1: Date, date2: Date): number {
    const timeDifference = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(timeDifference / (1000 * 3600 * 24));
  }

  // Format time (HH:MM)
  static formatTime(date: Date): string {
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  // Format date and time
  static formatDateTime(date: Date): string {
    return `${DateFormatter.formatDate(date)} ${DateFormatter.formatTime(date)}`;
  }

  // Create date from components
  static createDate(year: number, month: number, day: number): Date {
    return new Date(year, month - 1, day); // month is 0-indexed
  }

  // Get current date without time
  static getCurrentDate(): Date {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), now.getDate());
  }

  // Check if date is in the future
  static isFuture(date: Date): boolean {
    return date > new Date();
  }

  // Check if date is in the past
  static isPast(date: Date): boolean {
    return date < new Date();
  }
}