declare module 'react-native-apple-authentication' {
  export interface AppleRequestResponse {
    user: string;
    email?: string;
    fullName?: {
      givenName?: string;
      familyName?: string;
    };
    identityToken?: string;
    authorizationCode?: string;
  }

  export interface AppleAuthRequestOptions {
    requestedOperation: number;
    requestedScopes: number[];
  }

  export const appleAuth: {
    Operation: {
      LOGIN: number;
      REFRESH: number;
      LOGOUT: number;
    };
    Scope: {
      EMAIL: number;
      FULL_NAME: number;
    };
    performRequest: (options: AppleAuthRequestOptions) => Promise<AppleRequestResponse>;
    isSupported: boolean;
  };

  export const AppleButton: React.ComponentType<any>;
}
