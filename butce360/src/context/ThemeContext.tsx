import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import { storage } from '../services/storage';

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
  systemColorScheme: ColorSchemeName;
}

interface ThemeContextType {
  state: ThemeState;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

// Theme actions
type ThemeAction =
  | { type: 'SET_THEME_MODE'; payload: ThemeMode }
  | { type: 'SET_SYSTEM_COLOR_SCHEME'; payload: ColorSchemeName }
  | { type: 'UPDATE_IS_DARK'; payload: boolean };

// Storage key
const THEME_STORAGE_KEY = '@butce360_theme_mode';

// Initial state - Default to dark theme
const initialState: ThemeState = {
  mode: 'dark', // Default to dark mode instead of system
  isDark: true, // Default to dark
  systemColorScheme: Appearance.getColorScheme(),
};

// Theme reducer
const themeReducer = (state: ThemeState, action: ThemeAction): ThemeState => {
  switch (action.type) {
    case 'SET_THEME_MODE':
      return { ...state, mode: action.payload };
    case 'SET_SYSTEM_COLOR_SCHEME':
      return { ...state, systemColorScheme: action.payload };
    case 'UPDATE_IS_DARK':
      return { ...state, isDark: action.payload };
    default:
      return state;
  }
};

// Helper function to determine if theme should be dark
const getIsDark = (mode: ThemeMode, systemColorScheme: ColorSchemeName): boolean => {
  switch (mode) {
    case 'dark':
      return true;
    case 'light':
      return false;
    case 'system':
      return systemColorScheme === 'dark';
    default:
      return false;
  }
};

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider component
interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(themeReducer, initialState);

  // Load saved theme mode on app start
  useEffect(() => {
    const loadThemeMode = async () => {
      try {
        const savedMode = await storage.getItem(THEME_STORAGE_KEY);
        if (savedMode && ['light', 'dark', 'system'].includes(savedMode)) {
          dispatch({ type: 'SET_THEME_MODE', payload: savedMode as ThemeMode });
        }
      } catch (error) {
        console.error('[Theme] Error loading theme mode:', error);
      }
    };

    loadThemeMode();
  }, []);

  // Listen to system color scheme changes
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      dispatch({ type: 'SET_SYSTEM_COLOR_SCHEME', payload: colorScheme });
    });

    return () => subscription?.remove();
  }, []);

  // Update isDark when mode or system color scheme changes
  useEffect(() => {
    const isDark = getIsDark(state.mode, state.systemColorScheme);
    dispatch({ type: 'UPDATE_IS_DARK', payload: isDark });
  }, [state.mode, state.systemColorScheme]);

  // Set theme mode
  const setThemeMode = async (mode: ThemeMode) => {
    try {
      await storage.setItem(THEME_STORAGE_KEY, mode);
      dispatch({ type: 'SET_THEME_MODE', payload: mode });
    } catch (error) {
      console.error('[Theme] Error saving theme mode:', error);
    }
  };

  // Toggle between light and dark
  const toggleTheme = () => {
    const newMode = state.isDark ? 'light' : 'dark';
    setThemeMode(newMode);
  };

  const contextValue: ThemeContextType = {
    state,
    setThemeMode,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return context;
};
