require_relative '../node_modules/react-native/scripts/react_native_pods'

# Accept <PERSON><PERSON> as default but disable New Architecture
ENV['RCT_NEW_ARCH_ENABLED'] = '0'

platform :ios, '15.1'
prepare_react_native_project!

target 'butce360' do
  use_react_native!(
    :path => "../node_modules/react-native",
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    # Use default Hermes engine
    :fabric_enabled => false,
    :new_arch_enabled => false
  )

  # Manual pods for social login
  pod 'RNAppleAuthentication', :path => '../node_modules/@invertase/react-native-apple-authentication'
  pod 'RNGoogleSignin', :path => '../node_modules/@react-native-google-signin/google-signin'

  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.1'

        # Fix <PERSON><PERSON> compile issues
        if target.name == 'React-hermes'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= []
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'HERMES_ENABLE_DEBUGGER=0'
          config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++17'
          config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
        end

        # Fix Hermes engine compile issues
        if target.name == 'hermes-engine'
          config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++17'
          config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= []
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'HERMES_ENABLE_DEBUGGER=0'
        end
      end

      # Fix Hermes script phases for archive builds
      if target.name == 'hermes-engine'
        target.shell_script_build_phases.each do |phase|
          if phase.name&.include?('Hermes')
            puts "🔧 Patching Hermes script phase: #{phase.name}"
            phase.shell_script = <<~SCRIPT
              set -e
              echo "🚀 Hermes script phase (patched for archive)"

              # Skip in archive mode to avoid issues
              if [ "$CONFIGURATION" = "Release" ] && [ "$ARCHIVE_PATH" != "" ]; then
                echo "📦 Archive mode - skipping Hermes replacement"
                exit 0
              fi

              echo "✅ Hermes script completed"
            SCRIPT
          end
        end
      end

      # Fix React Native Codegen script phases
      if target.name == 'React-RCTFBReactNativeSpec'
        target.shell_script_build_phases.each do |phase|
          if phase.name&.include?('Check FBReactNativeSpec')
            puts "🔧 Patching Codegen script phase: #{phase.name}"
            phase.shell_script = <<~SCRIPT
              set -e
              echo "🚀 Codegen script phase (patched for archive)"

              # Always skip codegen check in archive builds
              if [ "$CONFIGURATION" = "Release" ]; then
                echo "📦 Release mode - skipping codegen check"
                exit 0
              fi

              echo "✅ Codegen script completed"
            SCRIPT
          end
        end
      end
    end
  end
end
