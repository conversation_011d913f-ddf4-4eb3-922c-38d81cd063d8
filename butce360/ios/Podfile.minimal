require_relative '../node_modules/react-native/scripts/react_native_pods'

platform :ios, '15.1'
prepare_react_native_project!

target 'butce360' do
  use_react_native!(
    :path => "../node_modules/react-native",
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # Manual pods for social login
  pod 'RNAppleAuthentication', :path => '../node_modules/@invertase/react-native-apple-authentication'
  pod 'RNGoogleSignin', :path => '../node_modules/@react-native-google-signin/google-signin'

  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.1'
      end
    end
  end
end
