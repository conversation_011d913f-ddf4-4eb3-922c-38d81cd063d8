import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const Header = ({ language, toggleLanguage }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <>
      {/* Top Bar */}
      <div className="bg-gradient-to-r from-emerald-600 to-blue-600 text-white py-3">
        <div className="container mx-auto px-6 flex justify-between items-center">
          <div className="text-sm font-medium">
            <span className="inline-flex items-center">
              🎉 <span className="ml-2 font-bold">Özel Fırsat!</span>
              <span className="ml-2">7 gün ücretsiz deneme + %25 indirim</span>
            </span>
          </div>
          <div className="flex items-center space-x-6">
            <Link to="/sss" className="text-sm hover:text-emerald-200 transition-colors font-medium">
              S.S.S
            </Link>
            <button
              onClick={toggleLanguage}
              className="text-sm bg-white/20 backdrop-blur-sm border border-white/30 px-3 py-1 rounded-full hover:bg-white hover:text-emerald-600 transition-all duration-300 font-medium"
            >
              {language === 'tr' ? '🇹🇷 TR' : '🇺🇸 EN'}
            </button>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header className="bg-white/95 backdrop-blur-md shadow-lg sticky top-0 z-50 border-b border-gray-100">
        <div className="container mx-auto px-6">
          <div className="flex justify-between items-center py-5">
            {/* Logo */}
            <div className="flex items-center">
              <Link to="/" className="text-3xl font-black bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                Butce360
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              <Link to="/nedir" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                Nedir ?
              </Link>
              <Link to="/nasil-calisir" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                Nasıl Çalışır ?
              </Link>
              <Link to="/ucretlendirme" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                Ücretlendirme
              </Link>
              <Link to="/iletisim" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                İletişim
              </Link>
              <Link to="/sss" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium">
                SSS
              </Link>
            </nav>

            {/* CTA Buttons */}
            <div className="hidden lg:flex items-center space-x-4">
              <a
                href="https://app.butce360.com/login"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
              >
                Giriş Yap
              </a>
              <a
                href="https://app.butce360.com/register"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-6 py-3 rounded-xl font-bold hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-emerald-500/25"
              >
                Ücretsiz Başla
              </a>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={toggleMenu}
              className="lg:hidden text-gray-700 hover:text-emerald-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden py-4 border-t border-gray-100">
              <nav className="flex flex-col space-y-4">
                <Link to="/nedir" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium" onClick={toggleMenu}>
                  Nedir ?
                </Link>
                <Link to="/nasil-calisir" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium" onClick={toggleMenu}>
                  Nasıl Çalışır ?
                </Link>
                <Link to="/ucretlendirme" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium" onClick={toggleMenu}>
                  Ücretlendirme
                </Link>
                <Link to="/iletisim" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium" onClick={toggleMenu}>
                  İletişim
                </Link>
                <Link to="/sss" className="text-gray-700 hover:text-emerald-600 transition-colors font-medium" onClick={toggleMenu}>
                  SSS
                </Link>
                <div className="flex flex-col space-y-3 pt-4 border-t border-gray-100">
                  <a
                    href="https://app.butce360.com/login"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
                  >
                    Giriş Yap
                  </a>
                  <a
                    href="https://app.butce360.com/register"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-6 py-3 rounded-xl font-bold hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 shadow-lg text-center"
                  >
                    Ücretsiz Başla
                  </a>
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>
    </>
  );
};

export default Header;
