import React from 'react';

const Hero = () => {
  return (
    <section className="min-h-screen flex items-center bg-white relative overflow-hidden">
      {/* Decorative Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-emerald-400 rounded-full blur-xl"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-blue-400 rounded-full blur-lg"></div>
        <div className="absolute bottom-32 left-1/4 w-20 h-20 bg-purple-400 rounded-full blur-lg"></div>
        <div className="absolute bottom-20 right-1/3 w-16 h-16 bg-orange-400 rounded-full blur-md"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center max-w-6xl mx-auto">
          {/* Main Heading */}
          <h1 className="text-5xl lg:text-8xl font-black text-gray-900 mb-8 leading-tight tracking-tight">
            <span className="block mb-2">Akıllı Bütçe</span>
            <span className="bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent">
              Yönetimi
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl lg:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed font-light">
            Gelir ve giderlerinizi takip edin, bütçenizi planlayın, finansal hedeflerinize ulaşın.
            <span className="font-bold text-gray-800 bg-yellow-100 px-2 py-1 rounded"> Yapay zeka destekli</span> 
            analiz ile paranızı daha akıllıca yönetin.
          </p>

          {/* Key Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 max-w-4xl mx-auto">
            <div className="bg-white border border-gray-100 rounded-3xl p-6 shadow-sm hover:shadow-lg transition-shadow">
              <div className="text-4xl font-black text-emerald-600 mb-3">5,000+</div>
              <div className="text-gray-600 font-medium">Aktif Kullanıcı</div>
            </div>
            <div className="bg-white border border-gray-100 rounded-3xl p-6 shadow-sm hover:shadow-lg transition-shadow">
              <div className="text-4xl font-black text-blue-600 mb-3">1M+</div>
              <div className="text-gray-600 font-medium">İşlem Takibi</div>
            </div>
            <div className="bg-white border border-gray-100 rounded-3xl p-6 shadow-sm hover:shadow-lg transition-shadow">
              <div className="text-4xl font-black text-purple-600 mb-3">%95</div>
              <div className="text-gray-600 font-medium">Memnuniyet Oranı</div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <a
              href="https://app.butce360.com/register"
              target="_blank"
              rel="noopener noreferrer"
              className="group bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-12 py-6 rounded-2xl text-xl font-bold hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 shadow-2xl hover:shadow-emerald-500/25 transform hover:-translate-y-1 text-center min-w-[250px]"
            >
              <span className="flex items-center justify-center">
                🚀 Hemen Başla
                <svg className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
            </a>

            <a
              href="/iletisim"
              className="group bg-white border-2 border-gray-200 text-gray-700 px-12 py-6 rounded-2xl text-xl font-bold hover:border-gray-300 hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl text-center min-w-[250px]"
            >
              <span className="flex items-center justify-center">
                📊 Demo İzle
                <svg className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
            </a>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-wrap items-center justify-center gap-8 text-sm text-gray-500">
            <div className="flex items-center bg-gray-50 px-4 py-2 rounded-full">
              <svg className="w-5 h-5 text-emerald-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="font-medium">7 Gün Ücretsiz</span>
            </div>
            <div className="flex items-center bg-gray-50 px-4 py-2 rounded-full">
              <svg className="w-5 h-5 text-emerald-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="font-medium">Kredi Kartı Yok</span>
            </div>
            <div className="flex items-center bg-gray-50 px-4 py-2 rounded-full">
              <svg className="w-5 h-5 text-emerald-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="font-medium">2 Dakika Kurulum</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
