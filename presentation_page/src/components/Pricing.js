import React from 'react';

const Pricing = () => {
  const plans = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      price: '0',
      period: 'Ücretsiz',
      description: '<PERSON><PERSON><PERSON>l kullanım için ideal',
      features: [
        '3 hesap bağlama',
        '100 işlem/ay',
        '<PERSON>mel raporlar',
        '<PERSON><PERSON> uygulam<PERSON>',
        '<PERSON><PERSON> destek'
      ],
      buttonText: 'Ücretsiz Başla',
      buttonClass: 'border-2 border-gray-300 text-gray-700 hover:border-emerald-500 hover:text-emerald-600',
      popular: false
    },
    {
      name: 'Profesyonel',
      price: '29',
      period: '/ay',
      description: 'Küçük işletmeler için',
      features: [
        '<PERSON>ın<PERSON>rs<PERSON><PERSON> hesap',
        '<PERSON><PERSON><PERSON><PERSON>rs<PERSON>z işlem',
        'Gelişmiş raporlar',
        'Bütçe planlama',
        'PDF ekstres analizi',
        'Öncelikli destek',
        'API erişimi'
      ],
      buttonText: '<PERSON><PERSON>',
      buttonClass: 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700',
      popular: true
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      price: '99',
      period: '/ay',
      description: 'Büyük organizasyonlar için',
      features: [
        'Tüm Profesyonel özellikler',
        'Çoklu kullanıcı yönetimi',
        'Özel raporlar',
        'Entegrasyon desteği',
        'Özel eğitim',
        '7/24 telefon desteği',
        'SLA garantisi'
      ],
      buttonText: 'İletişime Geç',
      buttonClass: 'border-2 border-gray-300 text-gray-700 hover:border-emerald-500 hover:text-emerald-600',
      popular: false
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-block bg-gray-100 rounded-full px-6 py-2 mb-6">
            <span className="text-gray-600 font-semibold text-sm">FİYATLANDIRMA</span>
          </div>
          <h2 className="text-4xl lg:text-6xl font-black text-gray-900 mb-6 leading-tight">
            Size Uygun
            <span className="block text-emerald-600">Planı Seçin</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            İhtiyacınıza uygun paketi seçin ve finansal takibin gücünü keşfedin. 
            Tüm paketlerde 7 gün ücretsiz deneme imkanı.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
            <div className="bg-emerald-100 text-emerald-800 px-6 py-3 rounded-full font-semibold">
              ✨ 7 gün ücretsiz deneme
            </div>
            <div className="bg-blue-100 text-blue-800 px-6 py-3 rounded-full font-semibold">
              💳 Kredi kartı gerektirmez
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`relative bg-white rounded-3xl p-8 border-2 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 ${
                plan.popular 
                  ? 'border-emerald-500 shadow-2xl shadow-emerald-500/20' 
                  : 'border-gray-200 hover:border-emerald-300'
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-6 py-2 rounded-full text-sm font-bold">
                    En Popüler
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                <div className="flex items-baseline justify-center">
                  <span className="text-5xl font-black text-gray-900">₺{plan.price}</span>
                  <span className="text-xl text-gray-600 ml-2">{plan.period}</span>
                </div>
              </div>

              {/* Features */}
              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <svg className="w-5 h-5 text-emerald-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <a
                href={plan.name === 'Kurumsal' ? '/iletisim' : 'https://app.butce360.com/register'}
                target={plan.name === 'Kurumsal' ? '_self' : '_blank'}
                rel={plan.name === 'Kurumsal' ? '' : 'noopener noreferrer'}
                className={`block w-full text-center px-8 py-4 rounded-2xl font-bold transition-all duration-300 ${plan.buttonClass}`}
              >
                {plan.buttonText}
              </a>
            </div>
          ))}
        </div>

        {/* Bottom Note */}
        <div className="text-center mt-16">
          <p className="text-gray-600 max-w-2xl mx-auto">
            Tüm planlar 30 gün para iade garantisi ile gelir. Sorularınız için 
            <a href="/iletisim" className="text-emerald-600 hover:text-emerald-700 font-semibold ml-1">
              bizimle iletişime geçin
            </a>.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
