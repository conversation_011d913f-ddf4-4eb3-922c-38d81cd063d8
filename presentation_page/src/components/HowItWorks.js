import React from 'react';

const HowItWorks = () => {
  const steps = [
    {
      step: '1',
      title: '<PERSON><PERSON><PERSON>',
      description: 'H<PERSON><PERSON>lı ve kolay kayıt işlemi ile hesabınızı oluşturun',
      icon: '👤'
    },
    {
      step: '2',
      title: 'He<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z<PERSON> Ekleyin',
      description: '<PERSON><PERSON> hesapları, kredi kartları ve nakit hesaplarınızı sisteme ekleyin',
      icon: '🏦'
    },
    {
      step: '3',
      title: 'İşlemlerinizi Kaydedin',
      description: '<PERSON><PERSON><PERSON> ve giderlerinizi manuel olarak girin veya banka ekstresini yükleyin',
      icon: '📝'
    },
    {
      step: '4',
      title: '<PERSON><PERSON>z <PERSON>in & Planlayın',
      description: 'Detaylı raporları inceleyin ve gelecek için bütçe planları oluşturun',
      icon: '📊'
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-block bg-white rounded-full px-6 py-2 mb-6 shadow-sm">
            <span className="text-gray-600 font-semibold text-sm">NASIL ÇALIŞIR</span>
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Nasıl Çalışır?
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Butce360 ile finansal takip sadece 4 adımda tamamlanır. 
            Basit ve kullanıcı dostu arayüzümüz ile dakikalar içinde başlayabilirsiniz.
          </p>
        </div>

        {/* Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {steps.map((step, index) => (
            <div key={index} className="text-center group">
              {/* Step Number */}
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  {step.step}
                </div>
                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-10 left-1/2 w-full h-0.5 bg-gradient-to-r from-emerald-300 to-emerald-400 transform translate-x-10"></div>
                )}
              </div>

              {/* Icon */}
              <div className="text-4xl mb-4">{step.icon}</div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors">
                {step.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {step.description}
              </p>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center bg-white rounded-3xl p-12 shadow-lg border border-gray-100">
          <h3 className="text-3xl font-bold text-gray-900 mb-6">
            Hemen Başlayın!
          </h3>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Finansal özgürlüğünüze giden yolculuk sadece birkaç tık uzağınızda.
            Bugün başlayın, yarın farkı görün.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://app.butce360.com/register"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-emerald-500/25"
            >
              Ücretsiz Hesap Oluştur
            </a>
            <a
              href="/iletisim"
              className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-2xl font-bold hover:border-emerald-500 hover:text-emerald-600 transition-colors"
            >
              Daha Fazla Bilgi
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
