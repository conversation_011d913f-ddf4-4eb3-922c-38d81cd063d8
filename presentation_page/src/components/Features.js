import React from 'react';

const Features = () => {
  const features = [
    {
      icon: '💰',
      title: '<PERSON><PERSON><PERSON> & Gider Takibi',
      description: 'Tüm gelir ve giderlerinizi kategorilere ayırarak detaylı takip edin',
      color: 'emerald'
    },
    {
      icon: '📊',
      title: 'Akıllı Raporlama',
      description: 'Harcama alışkanlıklarınızı analiz eden detaylı raporlar ve grafikler',
      color: 'blue'
    },
    {
      icon: '🎯',
      title: 'Bütçe Planlama',
      description: 'Kategorilere göre bütçe belirleyin ve hedeflerinizi takip edin',
      color: 'purple'
    },
    {
      icon: '🏦',
      title: 'Çoklu Hesap Yönetimi',
      description: 'Banka hesapları, kredi kartları ve nakit hesaplarınızı tek yerden yönetin',
      color: 'orange'
    },
    {
      icon: '📄',
      title: 'Banka Ekstresi Analizi',
      description: 'PDF banka ekstrelerinizi yükleyin, otomatik olarak işlemlerinizi kategorize edin',
      color: 'pink'
    },
    {
      icon: '🔒',
      title: 'Güvenli Veri Saklama',
      description: 'Finansal verileriniz SSL şifreleme ile güvenli şekilde korunur',
      color: 'indigo'
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      emerald: 'bg-emerald-50 border-emerald-200 text-emerald-600',
      blue: 'bg-blue-50 border-blue-200 text-blue-600',
      purple: 'bg-purple-50 border-purple-200 text-purple-600',
      orange: 'bg-orange-50 border-orange-200 text-orange-600',
      pink: 'bg-pink-50 border-pink-200 text-pink-600',
      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-600'
    };
    return colors[color] || colors.emerald;
  };

  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-block bg-gray-100 rounded-full px-6 py-2 mb-6">
            <span className="text-gray-600 font-semibold text-sm">ÖZELLİKLER</span>
          </div>
          <h2 className="text-4xl lg:text-6xl font-black text-gray-900 mb-6 leading-tight">
            Finansal Özgürlük,
            <span className="block text-emerald-600">Kolay Yönetim</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Butce360 ile finansal hayatınızı kontrol altına alın.
            Akıllı araçlar, basit arayüz, güçlü analiz.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group bg-white border border-gray-100 rounded-3xl p-8 hover:shadow-2xl hover:shadow-gray-200/50 transition-all duration-500 hover:-translate-y-2"
            >
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl border-2 mb-6 text-2xl ${getColorClasses(feature.color)}`}>
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center bg-gradient-to-br from-emerald-50 to-blue-50 rounded-3xl p-12 border border-emerald-100">
          <h3 className="text-3xl lg:text-4xl font-black text-gray-900 mb-6">
            Finansal Geleceğinizi Şekillendirin!
          </h3>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Binlerce kullanıcı Butce360 ile finansal hedeflerine ulaştı.
            Siz de bu başarıya ortak olun.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://app.butce360.com/register"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-emerald-600 text-white px-8 py-4 rounded-2xl font-bold hover:bg-emerald-700 transition-colors shadow-lg hover:shadow-emerald-500/25"
            >
              Ücretsiz Dene
            </a>
            <a
              href="/iletisim"
              className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-2xl font-bold hover:border-gray-400 hover:bg-white transition-colors"
            >
              Demo İste
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
