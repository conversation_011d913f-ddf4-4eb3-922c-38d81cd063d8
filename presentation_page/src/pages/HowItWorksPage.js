import React from 'react';
import HowItWorks from '../components/HowItWorks';

const HowItWorksPage = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Butce360
              <span className="text-emerald-600 block">Nasıl Çalışır?</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Sadece 4 basit adımda finansal takibi başlatın. 
              Teknik bilgi gerektirmez, dakikalar içinde kullanmaya başlayın.
            </p>
          </div>
        </div>
      </section>

      {/* How It Works Component */}
      <HowItWorks />

      {/* Detailed Steps */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Detaylı Adımlar
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Her adımda size rehberlik ediyoruz. Kolay kurulum, hızlı başlangıç.
              </p>
            </div>

            <div className="space-y-16">
              {/* Step 1 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                      1
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">Hesap Oluşturun</h3>
                  </div>
                  <p className="text-lg text-gray-600 mb-6">
                    Email adresiniz ile hızlı kayıt olun. Sadece birkaç dakika sürer ve 
                    kredi kartı bilgisi gerektirmez.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Email doğrulama</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Güvenli şifre oluşturma</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Profil bilgileri</span>
                    </li>
                  </ul>
                </div>
                <div className="bg-gradient-to-br from-emerald-100 to-blue-100 rounded-3xl p-8 text-center">
                  <div className="text-6xl mb-4">👤</div>
                  <h4 className="text-xl font-bold text-gray-900 mb-2">Hızlı Kayıt</h4>
                  <p className="text-gray-600">2 dakikada hesabınız hazır</p>
                </div>
              </div>

              {/* Step 2 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="order-2 lg:order-1">
                  <div className="bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl p-8 text-center">
                    <div className="text-6xl mb-4">🏦</div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">Güvenli Bağlantı</h4>
                    <p className="text-gray-600">Banka seviyesinde güvenlik</p>
                  </div>
                </div>
                <div className="order-1 lg:order-2">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                      2
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">Hesaplarınızı Ekleyin</h3>
                  </div>
                  <p className="text-lg text-gray-600 mb-6">
                    Banka hesaplarınızı, kredi kartlarınızı ve nakit hesaplarınızı sisteme ekleyin. 
                    Tüm finansal durumunuzu tek yerden görün.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Banka hesapları</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Kredi kartları</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Nakit hesaplar</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Step 3 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                      3
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">İşlemlerinizi Kaydedin</h3>
                  </div>
                  <p className="text-lg text-gray-600 mb-6">
                    Gelir ve giderlerinizi manuel olarak girin veya banka ekstresini yükleyin. 
                    Sistem otomatik olarak kategorilere ayırır.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Manuel işlem girişi</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">PDF ekstres yükleme</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Otomatik kategorilendirme</span>
                    </li>
                  </ul>
                </div>
                <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-3xl p-8 text-center">
                  <div className="text-6xl mb-4">📝</div>
                  <h4 className="text-xl font-bold text-gray-900 mb-2">Kolay Giriş</h4>
                  <p className="text-gray-600">Otomatik kategorilendirme</p>
                </div>
              </div>

              {/* Step 4 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="order-2 lg:order-1">
                  <div className="bg-gradient-to-br from-orange-100 to-red-100 rounded-3xl p-8 text-center">
                    <div className="text-6xl mb-4">📊</div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">Akıllı Analiz</h4>
                    <p className="text-gray-600">Detaylı raporlar ve öneriler</p>
                  </div>
                </div>
                <div className="order-1 lg:order-2">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                      4
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">Analiz Edin & Planlayın</h3>
                  </div>
                  <p className="text-lg text-gray-600 mb-6">
                    Detaylı raporları inceleyin, harcama alışkanlıklarınızı analiz edin ve 
                    gelecek için akıllı bütçe planları oluşturun.
                  </p>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Detaylı raporlar</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Bütçe planlama</span>
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">Finansal öneriler</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            Hemen Başlayın!
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Finansal özgürlüğünüze giden yolculuk sadece birkaç tık uzağınızda.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://app.butce360.com/register"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-colors shadow-lg"
            >
              Ücretsiz Hesap Oluştur
            </a>
            <a
              href="/iletisim"
              className="border-2 border-white text-white px-8 py-4 rounded-2xl font-bold hover:bg-white hover:text-emerald-600 transition-colors"
            >
              Daha Fazla Bilgi
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HowItWorksPage;
