import React from 'react';
import Features from '../components/Features';

const WhatIsPage = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Butce360
              <span className="text-emerald-600 block">Nedir?</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Butce360, kişisel ve kurumsal finansal yönetimi kolaylaştıran, 
              yapay zeka destekli akıllı bütçe takip platformudur.
            </p>
          </div>
        </div>
      </section>

      {/* What is Butce360 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                  Finansal Özgürlüğünüze Giden Yol
                </h2>
                <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                  Butce360, gelir ve giderlerinizi takip etmenizi, bütçe planlaması yapmanızı ve 
                  finansal hedeflerinize ulaşmanızı sağlayan kapsamlı bir platformdur.
                </p>
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  Banka ekstrelerinizi otomatik olarak analiz eder, harcama alışkanlıklarınızı 
                  raporlar ve size kişiselleştirilmiş finansal öneriler sunar.
                </p>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <svg className="w-6 h-6 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700 font-medium">Otomatik işlem kategorilendirme</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-6 h-6 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700 font-medium">Akıllı bütçe önerileri</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-6 h-6 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700 font-medium">Detaylı finansal raporlar</span>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-emerald-100 to-blue-100 rounded-3xl p-8 text-center">
                  <div className="text-6xl mb-4">📊</div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Akıllı Analiz</h3>
                  <p className="text-gray-600">
                    Yapay zeka algoritmaları ile harcama alışkanlıklarınızı analiz eder, 
                    tasarruf fırsatlarını keşfeder.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Neden Butce360?
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Finansal yönetimde karşılaştığınız zorlukları çözen, 
                hayatınızı kolaylaştıran özellikler.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <div className="text-4xl mb-4">⏱️</div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Zaman Tasarrufu</h3>
                <p className="text-gray-600">
                  Manuel hesap tutma derdine son. Otomatik kategorilendirme ile 
                  saatlerce zaman kazanın.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <div className="text-4xl mb-4">🎯</div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Hedef Odaklı</h3>
                <p className="text-gray-600">
                  Finansal hedeflerinizi belirleyin, ilerlemenizi takip edin ve 
                  hedefinize ulaşın.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <div className="text-4xl mb-4">📱</div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Her Yerden Erişim</h3>
                <p className="text-gray-600">
                  Web ve mobil uygulamalar ile finansal durumunuzu her yerden 
                  kontrol edin.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <div className="text-4xl mb-4">🔐</div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Güvenli</h3>
                <p className="text-gray-600">
                  Banka seviyesinde güvenlik ile verileriniz tamamen korunur. 
                  KVKK uyumlu veri işleme.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <div className="text-4xl mb-4">📈</div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Büyüme Odaklı</h3>
                <p className="text-gray-600">
                  Sadece takip değil, finansal büyümenizi destekleyen öneriler 
                  ve stratejiler.
                </p>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                <div className="text-4xl mb-4">🤝</div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Uzman Desteği</h3>
                <p className="text-gray-600">
                  Finansal danışmanlarımızdan destek alın, doğru kararlar verin.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Component */}
      <Features />

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            Finansal Özgürlüğünüze Başlayın
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Bugün Butce360'a katılın ve finansal geleceğinizi şekillendirmeye başlayın.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://app.butce360.com/register"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-colors shadow-lg"
            >
              Ücretsiz Başla
            </a>
            <a
              href="/iletisim"
              className="border-2 border-white text-white px-8 py-4 rounded-2xl font-bold hover:bg-white hover:text-emerald-600 transition-colors"
            >
              Demo İste
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default WhatIsPage;
