# Butce360 Presentation Page

Butce360 finansal takip uygulaması için modern ve responsive presentation website.

## Özellikler

- **Modern Tasarım**: Tailwind CSS ile responsive ve modern arayüz
- **React Router**: Çoklu sayfa navigasyonu
- **Responsive**: Tüm cihazlarda mükemmel görünüm
- **SEO Optimized**: Arama motorları için optimize edilmiş
- **Fast Loading**: Hızlı yükleme süreleri

## Sayfalar

- **Ana Sayfa**: Hero, özellikler, nasıl çalışır, fiyatlandırma ve iletişim
- **Nedir**: Butce360'ın detaylı tanıtımı
- **Nasıl Çalışır**: Adım adım kullanım rehberi
- **Fiyatlandırma**: Paket seçenekleri ve karşılaştırma
- **İletişim**: İletişim formu ve bilgileri
- **SSS**: <PERSON>ıkça sorulan sorular

## Kurulum

1. Bağımlılıkları yükleyin:
```bash
npm install
```

2. Geliştirme sunucusunu başlatın:
```bash
npm start
```

3. Tarayıcınızda `http://localhost:3000` adresini açın.

## Build

Production build oluşturmak için:

```bash
npm run build
```

## Teknolojiler

- **React 19**: Modern React framework
- **React Router Dom**: Client-side routing
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Modern icon library

## Özelleştirme

### Renkler
Tailwind config dosyasında (`tailwind.config.js`) renk paletini özelleştirebilirsiniz.

### İçerik
Tüm içerik component'lerde düzenlenebilir. Ana içerik dosyaları:
- `src/components/` - Yeniden kullanılabilir component'ler
- `src/pages/` - Sayfa component'leri

### Linkler
Tüm external linkler app.butce360.com'a yönlendirilmiştir. Gerekirse değiştirilebilir.

## Deployment

Build dosyalarını herhangi bir static hosting servisinde (Netlify, Vercel, GitHub Pages) deploy edebilirsiniz.

## Lisans

Bu proje NocyTech tarafından geliştirilmiştir.
