<!doctype html><html lang="en"><head><meta charset="utf-8"/><link rel="icon" href="/favicon.ico"/><meta name="viewport" content="width=device-width,initial-scale=1"/><meta name="theme-color" content="#000000"/><meta name="description" content="Web site created using create-react-app"/><link rel="manifest" href="/manifest.json"/><title>Bütçe 360</title><script defer="defer" src="/static/js/main.fa5a995c.js"></script><link href="/static/css/main.351bf1ef.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="root"></div><script>["log","info","warn","error","debug"].forEach(o=>{const e=console[o];console[o]=function(...o){if(o.length>0&&"string"==typeof o[0]){const e=o[0];if(e.includes("Download the React DevTools")||e.includes("react.dev/link/react-devtools"))return}e.apply(console,o)}})</script></body></html>