package routes

import (
	"net/http"

	"github.com/NocyTech/fin_notebook/pkg/domains/budget"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/middleware"
	"github.com/NocyTech/fin_notebook/pkg/state"
	"github.com/gin-gonic/gin"
)

func BudgetRoutes(r *gin.RouterGroup, s budget.Service) {
	g := r.Group("/budget")
	g.Use(middleware.Authorized())
	g.POST("", addBudget(s))
	g.PUT("/:id", editBudget(s))
	g.DELETE("/:id", deleteBudget(s))
	g.GET("", getBudgets(s))
}

// @Summary Create a new budget
// @Description Create a new budget for a specific category
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param budget body dtos.BudgetRequest true "Budget data"
// @Success 201 {object} map[string]interface{} "Budget created successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /budget [post]
func addBudget(s budget.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := state.GetCurrentUserID(c)

		var req dtos.BudgetRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid request data: " + err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		if err := s.CreateBudget(userID.String(), req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Failed to create budget: " + err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusCreated, gin.H{
			"message": "Budget created successfully",
			"status":  http.StatusCreated,
		})
	}
}

// @Summary Update an existing budget
// @Description Update an existing budget by ID
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Budget ID"
// @Param budget body dtos.BudgetRequest true "Budget data"
// @Success 200 {object} map[string]interface{} "Budget updated successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Budget not found"
// @Router /budget/{id} [put]
func editBudget(s budget.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := state.GetCurrentUserID(c)
		budgetID := c.Param("id")
		if budgetID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Budget ID is required",
				"status": http.StatusBadRequest,
			})
			return
		}

		var req dtos.BudgetRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid request data: " + err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		if err := s.UpdateBudget(userID.String(), budgetID, req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Failed to update budget: " + err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Budget updated successfully",
			"status":  http.StatusOK,
		})
	}
}

// @Summary Delete a budget
// @Description Delete a budget by ID
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Budget ID"
// @Success 200 {object} map[string]interface{} "Budget deleted successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Budget not found"
// @Router /budget/{id} [delete]
func deleteBudget(s budget.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := state.GetCurrentUserID(c)
		budgetID := c.Param("id")
		if budgetID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Budget ID is required",
				"status": http.StatusBadRequest,
			})
			return
		}

		if err := s.DeleteBudget(userID.String(), budgetID); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Failed to delete budget: " + err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Budget deleted successfully",
			"status":  http.StatusOK,
		})
	}
}

// @Summary Get all budgets
// @Description Get all budgets for the authenticated user
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "List of budgets"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /budget [get]
func getBudgets(s budget.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := state.GetCurrentUserID(c)

		budgets, err := s.GetAllBudgets(userID.String())
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Failed to get budgets: " + err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   budgets,
			"status": http.StatusOK,
		})
	}
}
