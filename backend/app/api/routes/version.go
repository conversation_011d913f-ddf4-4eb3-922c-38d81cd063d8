package routes

import (
	"github.com/NocyTech/fin_notebook/pkg/config"
	"github.com/NocyTech/fin_notebook/pkg/domains/version"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/localizer"
	"github.com/NocyTech/fin_notebook/pkg/middleware"
	"github.com/NocyTech/fin_notebook/pkg/state"
	"github.com/gin-gonic/gin"
)

func VersionRoutes(r *gin.RouterGroup, s version.Service) {
	g := r.Group("/version")
	g.GET("", middleware.FromClient(), middleware.Authorized(), getVersion(s))
	g.POST("", newVersion(s))
}

func getVersion(s version.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetVersion(c)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_process", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}
		c.JSO<PERSON>(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func newVersion(s version.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForNewVersion
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		// -----> force update key check for just update
		force_update_key := c.Request.Header.Get("force_update_key")
		if force_update_key != config.InitConfig().App.ForceUpdateKey {
			c.AbortWithStatusJSON(401, gin.H{
				"error": "key is wrong",
			})
			return
		}
		if err := s.NewVersion(c, req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(201, gin.H{
			"data":   "success",
			"status": 201,
		})
	}
}
