package routes

import (
	"github.com/NocyTech/fin_notebook/pkg/domains/account"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/middleware"
	"github.com/NocyTech/fin_notebook/pkg/state"
	"github.com/gin-gonic/gin"
)

func AccountRoutes(r *gin.RouterGroup, s account.Service) {
	g := r.Group("/accounts")
	g.Use(middleware.Authorized())

	g.POST("", CreateAccount(s))
	g.GET("", GetAllAccounts(s))
	g.GET("/:id", GetAccountByID(s))
	g.PUT("/:id", UpdateAccount(s))
	g.DELETE("/:id", DeleteAccount(s))
}

// @Summary Create account
// @Description Create a new financial account
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.AccountRequest true "Account data"
// @Success 201 {object} map[string]interface{} "Returns created account"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /accounts [post]
func CreateAccount(s account.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.AccountRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.CreateAccount(userID.String(), &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Get all accounts
// @Description Get all financial accounts for the current user
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Returns list of accounts"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /accounts [get]
func GetAllAccounts(s account.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetAllAccounts(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get account by ID
// @Description Get a specific account by its ID
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Account ID"
// @Success 200 {object} map[string]interface{} "Returns account details"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /accounts/{id} [get]
func GetAccountByID(s account.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetAccountByID(id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Update account
// @Description Update an existing account
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Account ID"
// @Param request body dtos.AccountRequest true "Updated account data"
// @Success 200 {object} map[string]interface{} "Returns updated account"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /accounts/{id} [put]
func UpdateAccount(s account.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		var req dtos.AccountRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateAccount(id, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete account
// @Description Delete an account by ID
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Account ID"
// @Success 200 {object} map[string]interface{} "Success message"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /accounts/{id} [delete]
func DeleteAccount(s account.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		if err := s.DeleteAccount(id); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   "account deleted successfully",
			"status": 200,
		})
	}
}
