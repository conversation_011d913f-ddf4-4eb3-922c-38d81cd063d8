package routes

import (
	"github.com/NocyTech/fin_notebook/pkg/domains/bank_statement"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/state"
	"github.com/gin-gonic/gin"
)

func BankStatementRoutes(r *gin.RouterGroup, s bank_statement.Service) {
	g := r.Group("/bank-statements")
	// g.Use(middleware.Authorized())

	g.POST("/upload", UploadBankStatement(s))
	g.POST("/import", ImportBankStatementEntries(s))
}

// @Summary Upload bank statement
// @Description Upload a bank statement PDF and extract transactions
// @Tags bank-statements
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param file formData file true "Bank statement PDF file"
// @Param bank formData string true "Bank name (vakifbank or enpara)"
// @Success 200 {object} map[string]interface{} "Returns extracted transactions"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /bank-statements/upload [post]
func UploadBankStatement(s bank_statement.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		// Get bank parameter
		bankName := c.PostForm("bank")
		if bankName == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Bank parameter is required",
				"status": 400,
			})
			return
		}

		// Validate bank name
		if bankName != "vakifbank" && bankName != "enpara" && bankName != "garanti" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid bank name. Supported banks: vakifbank, enpara, garanti",
				"status": 400,
			})
			return
		}

		// Get the file from the request
		file, err := c.FormFile("file")
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "No file uploaded or invalid file",
				"status": 400,
			})
			return
		}

		// Check file extension
		if !isValidPDFFile(file.Filename) {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Only PDF files are allowed",
				"status": 400,
			})
			return
		}

		// Open the file
		fileContent, err := file.Open()
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Failed to open file: " + err.Error(),
				"status": 400,
			})
			return
		}
		defer fileContent.Close()

		// Parse the PDF based on bank type
		var entries []dtos.BankStatementEntry
		switch bankName {
		case "vakifbank":
			entries, err = s.ParseVakifbankStatement(userID.String(), fileContent)
		case "enpara":
			entries, err = s.ParseEnparaStatement(userID.String(), fileContent)
		case "garanti":
			entries, err = s.ParseGarantiStatement(userID.String(), fileContent)
		default:
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Unsupported bank type",
				"status": 400,
			})
			return
		}

		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Failed to parse PDF: " + err.Error(),
				"status": 400,
			})
			return
		}

		// Convert to DTO
		var dtoEntries []dtos.BankStatementEntry
		for _, entry := range entries {
			dtoEntries = append(dtoEntries, dtos.BankStatementEntry{
				Date:        entry.Date,
				Description: entry.Description,
				Amount:      entry.Amount,
				Type:        entry.Type,
				CategoryID:  entry.CategoryID,
				AccountID:   entry.AccountID,
			})
		}

		// Return the extracted transactions
		c.JSON(200, gin.H{
			"data": dtos.BankStatementUploadResponse{
				Entries: dtoEntries,
			},
			"status": 200,
		})
	}
}

// @Summary Import bank statement entries
// @Description Import selected bank statement entries as transactions
// @Tags bank-statements
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body []dtos.BankStatementImportRequest true "Bank statement entries to import"
// @Success 200 {object} map[string]interface{} "Returns created transactions"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /bank-statements/import [post]
func ImportBankStatementEntries(s bank_statement.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		// Parse request body
		var req []dtos.BankStatementImportRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Validate request
		if len(req) == 0 {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "No entries to import",
				"status": 400,
			})
			return
		}

		// Import entries
		transactions, err := s.ImportBankStatementEntries(userID.String(), req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Return the created transactions
		c.JSON(200, gin.H{
			"data": dtos.BankStatementImportResponse{
				Transactions: transactions,
			},
			"status": 200,
		})
	}
}

// isValidPDFFile checks if the file has a PDF extension
func isValidPDFFile(filename string) bool {
	return len(filename) > 4 && filename[len(filename)-4:] == ".pdf"
}
