package utils

import (
	"strings"
)

// Common transaction descriptions and their corresponding category mappings
var categoryKeywords = map[string]string{
	// Income categories
	"maaş":         "income_salary",
	"salary":       "income_salary",
	"ücret":        "income_salary",
	"ödeme":        "income_payment",
	"payment":      "income_payment",
	"gelen eft":    "income_transfer",
	"gelen havale": "income_transfer",
	"gelen fast":   "income_transfer",
	"faiz":         "income_interest",
	"interest":     "income_interest",
	"temettü":      "income_dividend",
	"dividend":     "income_dividend",
	"kira geliri":  "income_rent",
	"rent income":  "income_rent",

	// Expense categories
	"market":        "expense_groceries",
	"süpermarket":   "expense_groceries",
	"grocery":       "expense_groceries",
	"restoran":      "expense_dining",
	"restaurant":    "expense_dining",
	"cafe":          "expense_dining",
	"kafe":          "expense_dining",
	"yemek":         "expense_dining",
	"food":          "expense_dining",
	"akaryakıt":     "expense_transportation",
	"benzin":        "expense_transportation",
	"fuel":          "expense_transportation",
	"gas":           "expense_transportation",
	"taksi":         "expense_transportation",
	"taxi":          "expense_transportation",
	"ulaşım":        "expense_transportation",
	"transport":     "expense_transportation",
	"elektrik":      "expense_utilities",
	"electricity":   "expense_utilities",
	"su":            "expense_utilities",
	"water":         "expense_utilities",
	"doğalgaz":      "expense_utilities",
	"gas bill":      "expense_utilities",
	"telefon":       "expense_utilities",
	"phone":         "expense_utilities",
	"internet":      "expense_utilities",
	"kira gideri":   "expense_housing",
	"rent payment":  "expense_housing",
	"aidat":         "expense_housing",
	"mortgage":      "expense_housing",
	"sağlık":        "expense_healthcare",
	"health":        "expense_healthcare",
	"hastane":       "expense_healthcare",
	"hospital":      "expense_healthcare",
	"doktor":        "expense_healthcare",
	"doctor":        "expense_healthcare",
	"eczane":        "expense_healthcare",
	"pharmacy":      "expense_healthcare",
	"eğitim":        "expense_education",
	"education":     "expense_education",
	"okul":          "expense_education",
	"school":        "expense_education",
	"kurs":          "expense_education",
	"course":        "expense_education",
	"giyim":         "expense_shopping",
	"clothing":      "expense_shopping",
	"alışveriş":     "expense_shopping",
	"shopping":      "expense_shopping",
	"eğlence":       "expense_entertainment",
	"entertainment": "expense_entertainment",
	"sinema":        "expense_entertainment",
	"cinema":        "expense_entertainment",
	"tiyatro":       "expense_entertainment",
	"theater":       "expense_entertainment",
	"konser":        "expense_entertainment",
	"concert":       "expense_entertainment",
	"sigorta":       "expense_insurance",
	"insurance":     "expense_insurance",
	"vergi":         "expense_tax",
	"tax":           "expense_tax",
	"masraf":        "expense_fee",
	"fee":           "expense_fee",
	"komisyon":      "expense_fee",
	"commission":    "expense_fee",
	"atm":           "expense_withdrawal",
	"para çek":      "expense_withdrawal",
	"withdrawal":    "expense_withdrawal",

	// Transfer categories
	"virman":   "transfer",
	"transfer": "transfer",
	"eft":      "transfer",
	"havale":   "transfer",
	"fast":     "transfer",
}

// Default categories by transaction type
var defaultCategories = map[string]string{
	"income":   "income_other",
	"expense":  "expense_other",
	"transfer": "transfer",
}

// CategoryIDMap maps category codes to their UUIDs
// These should be replaced with actual UUIDs from your database
var CategoryIDMap = map[string]string{
	"income_salary":   "00000000-0000-0000-0000-000000000001",
	"income_payment":  "00000000-0000-0000-0000-000000000002",
	"income_transfer": "00000000-0000-0000-0000-000000000003",
	"income_interest": "00000000-0000-0000-0000-000000000004",
	"income_dividend": "00000000-0000-0000-0000-000000000005",
	"income_rent":     "00000000-0000-0000-0000-000000000006",
	"income_other":    "00000000-0000-0000-0000-000000000007",

	"expense_groceries":      "00000000-0000-0000-0000-000000000008",
	"expense_dining":         "00000000-0000-0000-0000-000000000009",
	"expense_transportation": "00000000-0000-0000-0000-000000000010",
	"expense_utilities":      "00000000-0000-0000-0000-000000000011",
	"expense_housing":        "00000000-0000-0000-0000-000000000012",
	"expense_healthcare":     "00000000-0000-0000-0000-000000000013",
	"expense_education":      "00000000-0000-0000-0000-000000000014",
	"expense_shopping":       "00000000-0000-0000-0000-000000000015",
	"expense_entertainment":  "00000000-0000-0000-0000-000000000016",
	"expense_insurance":      "00000000-0000-0000-0000-000000000017",
	"expense_tax":            "00000000-0000-0000-0000-000000000018",
	"expense_fee":            "00000000-0000-0000-0000-000000000019",
	"expense_withdrawal":     "00000000-0000-0000-0000-000000000020",
	"expense_other":          "00000000-0000-0000-0000-000000000021",

	"transfer": "00000000-0000-0000-0000-000000000022",
}

// GetCategoryIDForTransaction determines the appropriate category ID for a transaction
// based on its description and type
func GetCategoryIDForTransaction(description string, transactionType string) string {
	// Convert description to lowercase for case-insensitive matching
	lowerDesc := strings.ToLower(description)

	// Check for keyword matches
	for keyword, category := range categoryKeywords {
		if strings.Contains(lowerDesc, keyword) {
			if categoryID, ok := CategoryIDMap[category]; ok {
				return categoryID
			}
		}
	}

	// If no match found, use default category based on transaction type
	if defaultCategory, ok := defaultCategories[transactionType]; ok {
		if categoryID, ok := CategoryIDMap[defaultCategory]; ok {
			return categoryID
		}
	}

	// Fallback to "other" category
	return CategoryIDMap["expense_other"]
}
