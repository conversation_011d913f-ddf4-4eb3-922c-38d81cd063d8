package utils

import (
	"strings"
)

// Common bank names and their corresponding account mappings
var bankKeywords = map[string]string{
	// Bank names in Turkish
	"vakıfbank":     "vakifbank",
	"vakif":         "vakifbank",
	"ziraat":        "ziraat",
	"halkbank":      "halkbank",
	"garanti":       "garanti",
	"yapı kredi":    "yapikredi",
	"yapi kredi":    "yapikredi",
	"iş bankası":    "isbank",
	"is bankasi":    "isbank",
	"akbank":        "akbank",
	"denizbank":     "denizbank",
	"qnb":           "qnb_finansbank",
	"finansbank":    "qnb_finansbank",
	"ing":           "ing",
	"hsbc":          "hsbc",
	"teb":           "teb",
	"odeabank":      "odeabank",
	"şekerbank":     "sekerbank",
	"sekerbank":     "sekerbank",
	"albaraka":      "albaraka",
	"kuveyt türk":   "kuveytturk",
	"kuveyt turk":   "kuveytturk",
	"türkiye finans": "turkiyefinans",
	"turkiye finans": "turkiyefinans",
	
	// English bank names
	"bank of america": "bank_of_america",
	"chase":         "chase",
	"wells fargo":   "wells_fargo",
	"citibank":      "citibank",
	"citi":          "citibank",
	"capital one":   "capital_one",
	"td bank":       "td_bank",
	"pnc":           "pnc",
	"bank":          "default_bank",
}

// AccountIDMap maps bank codes to their UUIDs
// These should be replaced with actual UUIDs from your database
var AccountIDMap = map[string]string{
	"vakifbank":      "********-0000-0000-0000-********0101",
	"ziraat":         "********-0000-0000-0000-********0102",
	"halkbank":       "********-0000-0000-0000-********0103",
	"garanti":        "********-0000-0000-0000-********0104",
	"yapikredi":      "********-0000-0000-0000-************",
	"isbank":         "********-0000-0000-0000-********0106",
	"akbank":         "********-0000-0000-0000-********0107",
	"denizbank":      "********-0000-0000-0000-********0108",
	"qnb_finansbank": "********-0000-0000-0000-************",
	"ing":            "********-0000-0000-0000-********0110",
	"hsbc":           "********-0000-0000-0000-********0111",
	"teb":            "********-0000-0000-0000-********0112",
	"odeabank":       "********-0000-0000-0000-********0113",
	"sekerbank":      "********-0000-0000-0000-********0114",
	"albaraka":       "********-0000-0000-0000-********0115",
	"kuveytturk":     "********-0000-0000-0000-********0116",
	"turkiyefinans":  "********-0000-0000-0000-********0117",
	"default_bank":   "********-0000-0000-0000-************", // Default account
}

// GetAccountIDForTransaction determines the appropriate account ID for a transaction
// based on its description
func GetAccountIDForTransaction(description string) string {
	// Convert description to lowercase for case-insensitive matching
	lowerDesc := strings.ToLower(description)
	
	// Check for bank name matches
	for keyword, bank := range bankKeywords {
		if strings.Contains(lowerDesc, keyword) {
			if accountID, ok := AccountIDMap[bank]; ok {
				return accountID
			}
		}
	}
	
	// If no match found, use default account
	return AccountIDMap["default_bank"]
}
