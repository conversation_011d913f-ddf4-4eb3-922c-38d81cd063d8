package dtos

// BankStatementUploadResponse represents the response for a bank statement upload
type BankStatementUploadResponse struct {
	Entries []BankStatementEntry `json:"entries"`
}

// BankStatementEntry represents a single transaction entry from a bank statement
type BankStatementEntry struct {
	Date        string  `json:"date"`
	Description string  `json:"description"`
	Amount      float64 `json:"amount"`
	Type        string  `json:"type"`        // "expense" or "income"
	CategoryID  string  `json:"category_id"` // Auto-assigned category ID
	AccountID   string  `json:"account_id"`  // Auto-assigned account ID
}

// BankStatementImportRequest represents a request to import a bank statement entry
type BankStatementImportRequest struct {
	Date        string  `json:"date" binding:"required"`
	Description string  `json:"description" binding:"required"`
	Amount      float64 `json:"amount" binding:"required,gt=0"`
	Type        string  `json:"type" binding:"required,oneof=expense income"`
	CategoryID  string  `json:"category_id"` // Auto-assigned, not required from user
	AccountID   string  `json:"account_id"`  // Optional, will use default if not provided
}

// BankStatementImportResponse represents the response for a bank statement import
type BankStatementImportResponse struct {
	Transactions []TransactionResponse `json:"transactions"`
}
