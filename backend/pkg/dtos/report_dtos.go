package dtos

// SummaryReportResponse represents the summary report response
type SummaryReportResponse struct {
	TotalIncome          float64             `json:"total_income"`
	TotalExpense         float64             `json:"total_expense"`
	NetBalance           float64             `json:"net_balance"`
	TotalBalance         float64             `json:"total_balance"`
	MonthlyIncome        float64             `json:"monthly_income"`
	MonthlyExpenses      float64             `json:"monthly_expenses"`
	TotalTransactions    int                 `json:"total_transactions"`
	BalanceChange        string              `json:"balance_change"`
	IncomeChange         string              `json:"income_change"`
	ExpenseChange        string              `json:"expense_change"`
	TransactionChange    string              `json:"transaction_change"`
	TopExpenseCategories []CategoryAmountDTO `json:"top_expense_categories"`
	TopIncomeCategories  []CategoryAmountDTO `json:"top_income_categories"`
}

// CategoryAmountDTO represents the category with amount
type CategoryAmountDTO struct {
	Category string  `json:"category"`
	Amount   float64 `json:"amount"`
}

// CategoryBreakdownResponse represents the category breakdown response
type CategoryBreakdownResponse struct {
	ExpenseCategories []CategoryAmountDTO `json:"expense_categories"`
	IncomeCategories  []CategoryAmountDTO `json:"income_categories"`
}

// MonthlyReportResponse represents the monthly report response
type MonthlyReportResponse struct {
	Months []MonthlyDataDTO `json:"months"`
}

// MonthlyDataDTO represents the monthly data
type MonthlyDataDTO struct {
	Month   string  `json:"month"`
	Income  float64 `json:"income"`
	Expense float64 `json:"expense"`
}

// LocationSummaryResponse represents the location summary response
type LocationSummaryResponse struct {
	Locations []LocationAmountDTO `json:"locations"`
}

// LocationAmountDTO represents the location with amount
type LocationAmountDTO struct {
	Location string  `json:"location"`
	Amount   float64 `json:"amount"`
}
