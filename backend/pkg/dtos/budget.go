package dtos

type BudgetRequest struct {
	Name       string  `json:"name"`
	Amount     float64 `json:"amount" binding:"required,gt=0"`
	Period     string  `json:"period" binding:"required,oneof=monthly yearly"`
	CategoryId string  `json:"category_id" binding:"required,uuid"`
	Color      string  `json:"color"`
}

type BudgetResponse struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	Amount       float64 `json:"amount"`
	Period       string  `json:"period"`
	CategoryId   string  `json:"category_id"`
	CategoryName string  `json:"category_name"`
	Spent        float64 `json:"spent"`
	CreatedAt    string  `json:"created_at"`
	UpdatedAt    string  `json:"updated_at"`
	Color        string  `json:"color"`
}
