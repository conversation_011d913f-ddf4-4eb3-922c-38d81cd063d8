package dtos

// Google OAuth Request
type GoogleOAuthRequest struct {
	IDToken     string `json:"id_token" binding:"required"`
	AccessToken string `json:"access_token"`
}

// Apple OAuth Request
type AppleOAuthRequest struct {
	IdentityToken     string `json:"identity_token" binding:"required"`
	AuthorizationCode string `json:"authorization_code"`
	UserIdentifier    string `json:"user_identifier" binding:"required"`
	Email             string `json:"email"`
	FullName          struct {
		GivenName  string `json:"given_name"`
		FamilyName string `json:"family_name"`
	} `json:"full_name"`
}

// OAuth Response
type OAuthResponse struct {
	Success      bool   `json:"success"`
	Token        string `json:"token,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
	User         struct {
		ID              string `json:"id"`
		Email           string `json:"email"`
		Name            string `json:"name"`
		ProfileImageURL string `json:"profile_image_url,omitempty"`
		Provider        string `json:"provider"`
		IsNewUser       bool   `json:"is_new_user"`
	} `json:"user,omitempty"`
	Error string `json:"error,omitempty"`
}

// Google User Info (from Google API)
type GoogleUserInfo struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verified_email"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
	Locale        string `json:"locale"`
}

// Apple User Info (from Apple ID Token)
type AppleUserInfo struct {
	Sub            string `json:"sub"`
	Email          string `json:"email"`
	EmailVerified  string `json:"email_verified"`
	IsPrivateEmail string `json:"is_private_email"`
	AuthTime       int64  `json:"auth_time"`
	NonceSupported bool   `json:"nonce_supported"`
}
