package dtos

import (
	"time"
)

// TransactionRequest represents the transaction creation request
type TransactionRequest struct {
	Title           string    `json:"title" binding:"required"`
	Type            string    `json:"type" binding:"required,oneof=expense income"`
	Amount          float64   `json:"amount" binding:"required,gt=0"`
	Currency        string    `json:"currency" binding:"required"`
	CategoryID      string    `json:"category_id" binding:"omitempty,uuid"`
	PaymentMethod   string    `json:"payment_method" binding:"required"`
	AccountID       string    `json:"account_id" binding:"omitempty,uuid"`
	Note            string    `json:"note"`
	TransactionDate time.Time `json:"transaction_date" binding:"required"`
	Location        string    `json:"location"`
}

// BulkTransactionRequest represents the bulk transaction creation request
type BulkTransactionRequest struct {
	Transactions []TransactionRequest `json:"transactions" binding:"required,min=1,max=50,dive"`
}

// BulkTransactionResponse represents the bulk transaction creation response
type BulkTransactionResponse struct {
	SuccessCount int                    `json:"success_count"`
	FailureCount int                    `json:"failure_count"`
	Successful   []TransactionResponse  `json:"successful"`
	Failed       []BulkTransactionError `json:"failed"`
}

// BulkTransactionError represents an error for a specific transaction in bulk operation
type BulkTransactionError struct {
	Index   int                `json:"index"`
	Error   string             `json:"error"`
	Request TransactionRequest `json:"request"`
}

// TransactionResponse represents the transaction response
type TransactionResponse struct {
	ID              string      `json:"id"`
	Title           string      `json:"title"`
	Type            string      `json:"type"`
	Amount          float64     `json:"amount"`
	Currency        string      `json:"currency"`
	CategoryID      string      `json:"category_id"`
	Category        CategoryDTO `json:"category"`
	PaymentMethod   string      `json:"payment_method"`
	AccountID       string      `json:"account_id"`
	Account         AccountDTO  `json:"account"`
	Note            string      `json:"note"`
	TransactionDate time.Time   `json:"transaction_date"`
	Location        string      `json:"location"`
	CreatedAt       time.Time   `json:"created_at"`
	UpdatedAt       time.Time   `json:"updated_at"`
}

// TransactionUpdateRequest represents the transaction update request
type TransactionUpdateRequest struct {
	Title           string    `json:"title"`
	Amount          float64   `json:"amount"`
	Currency        string    `json:"currency"`
	CategoryID      string    `json:"category_id"`
	PaymentMethod   string    `json:"payment_method"`
	AccountID       string    `json:"account_id"`
	Note            string    `json:"note"`
	TransactionDate time.Time `json:"transaction_date"`
	Location        string    `json:"location"`
}

// TransactionFilterRequest represents the transaction filter request
type TransactionFilterRequest struct {
	Type          string  `form:"type" binding:"omitempty,oneof=income expense"`
	StartDate     string  `form:"start_date"`
	EndDate       string  `form:"end_date"`
	CategoryID    string  `form:"category_id"`
	PaymentMethod string  `form:"payment_method"`
	AccountID     string  `form:"account_id"`
	MinAmount     float64 `form:"min_amount"`
	MaxAmount     float64 `form:"max_amount"`
	Search        string  `form:"search"`
	Page          int     `form:"page,default=1"`
	Limit         int     `form:"limit,default=10"`
}
