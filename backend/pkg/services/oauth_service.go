package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type OAuthService struct {
	db *gorm.DB
}

func NewOAuthService(db *gorm.DB) *OAuthService {
	return &OAuthService{
		db: db,
	}
}

// Google OAuth Authentication
func (s *OAuthService) AuthenticateWithGoogle(req *dtos.GoogleOAuthRequest) (*dtos.OAuthResponse, error) {
	// Verify Google ID Token
	googleUser, err := s.verifyGoogleIDToken(req.IDToken)
	if err != nil {
		return &dtos.OAuthResponse{
			Success: false,
			Error:   "Invalid Google ID token",
		}, err
	}

	// Check if user exists with Google ID
	var user entities.User
	result := s.db.Where("google_id = ?", googleUser.ID).First(&user)

	isNewUser := false
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// Check if user exists with same email
			emailResult := s.db.Where("email = ?", googleUser.Email).First(&user)
			if emailResult.Error != nil {
				if errors.Is(emailResult.Error, gorm.ErrRecordNotFound) {
					// Create new user
					user, err = s.createGoogleUser(googleUser, req)
					if err != nil {
						return &dtos.OAuthResponse{
							Success: false,
							Error:   "Failed to create user",
						}, err
					}
					isNewUser = true
				} else {
					return &dtos.OAuthResponse{
						Success: false,
						Error:   "Database error",
					}, emailResult.Error
				}
			} else {
				// Link Google account to existing user
				err = s.linkGoogleAccount(&user, googleUser, req)
				if err != nil {
					return &dtos.OAuthResponse{
						Success: false,
						Error:   "Failed to link Google account",
					}, err
				}
			}
		} else {
			return &dtos.OAuthResponse{
				Success: false,
				Error:   "Database error",
			}, result.Error
		}
	}

	// Generate JWT token (simplified)
	token := fmt.Sprintf("jwt_token_%s_%d", user.ID.String(), time.Now().Unix())
	refreshToken := fmt.Sprintf("refresh_token_%s_%d", user.ID.String(), time.Now().Unix())

	// Update last login
	s.db.Model(&user).Update("last_login", time.Now().Format(time.RFC3339))

	return &dtos.OAuthResponse{
		Success:      true,
		Token:        token,
		RefreshToken: refreshToken,
		User: struct {
			ID              string `json:"id"`
			Email           string `json:"email"`
			Name            string `json:"name"`
			ProfileImageURL string `json:"profile_image_url,omitempty"`
			Provider        string `json:"provider"`
			IsNewUser       bool   `json:"is_new_user"`
		}{
			ID:              user.ID.String(),
			Email:           user.Email,
			Name:            user.Name,
			ProfileImageURL: *user.ProfileImageURL,
			Provider:        "google",
			IsNewUser:       isNewUser,
		},
	}, nil
}

// Apple OAuth Authentication
func (s *OAuthService) AuthenticateWithApple(req *dtos.AppleOAuthRequest) (*dtos.OAuthResponse, error) {
	// Verify Apple Identity Token
	appleUser, err := s.verifyAppleIdentityToken(req.IdentityToken)
	if err != nil {
		return &dtos.OAuthResponse{
			Success: false,
			Error:   "Invalid Apple identity token",
		}, err
	}

	// Check if user exists with Apple ID
	var user entities.User
	result := s.db.Where("apple_id = ?", appleUser.Sub).First(&user)

	isNewUser := false
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// Check if user exists with same email
			if req.Email != "" {
				emailResult := s.db.Where("email = ?", req.Email).First(&user)
				if emailResult.Error != nil {
					if errors.Is(emailResult.Error, gorm.ErrRecordNotFound) {
						// Create new user
						user, err = s.createAppleUser(appleUser, req)
						if err != nil {
							return &dtos.OAuthResponse{
								Success: false,
								Error:   "Failed to create user",
							}, err
						}
						isNewUser = true
					} else {
						return &dtos.OAuthResponse{
							Success: false,
							Error:   "Database error",
						}, emailResult.Error
					}
				} else {
					// Link Apple account to existing user
					err = s.linkAppleAccount(&user, appleUser, req)
					if err != nil {
						return &dtos.OAuthResponse{
							Success: false,
							Error:   "Failed to link Apple account",
						}, err
					}
				}
			} else {
				// Create user without email (Apple private relay)
				user, err = s.createAppleUser(appleUser, req)
				if err != nil {
					return &dtos.OAuthResponse{
						Success: false,
						Error:   "Failed to create user",
					}, err
				}
				isNewUser = true
			}
		} else {
			return &dtos.OAuthResponse{
				Success: false,
				Error:   "Database error",
			}, result.Error
		}
	}

	// Generate JWT token (simplified)
	token := fmt.Sprintf("jwt_token_%s_%d", user.ID.String(), time.Now().Unix())
	refreshToken := fmt.Sprintf("refresh_token_%s_%d", user.ID.String(), time.Now().Unix())

	// Update last login
	s.db.Model(&user).Update("last_login", time.Now().Format(time.RFC3339))

	return &dtos.OAuthResponse{
		Success:      true,
		Token:        token,
		RefreshToken: refreshToken,
		User: struct {
			ID              string `json:"id"`
			Email           string `json:"email"`
			Name            string `json:"name"`
			ProfileImageURL string `json:"profile_image_url,omitempty"`
			Provider        string `json:"provider"`
			IsNewUser       bool   `json:"is_new_user"`
		}{
			ID:        user.ID.String(),
			Email:     user.Email,
			Name:      user.Name,
			Provider:  "apple",
			IsNewUser: isNewUser,
		},
	}, nil
}

// Verify Google ID Token
func (s *OAuthService) verifyGoogleIDToken(idToken string) (*dtos.GoogleUserInfo, error) {
	// Call Google's tokeninfo endpoint
	resp, err := http.Get(fmt.Sprintf("https://oauth2.googleapis.com/tokeninfo?id_token=%s", idToken))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, errors.New("invalid Google ID token")
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var googleUser dtos.GoogleUserInfo
	if err := json.Unmarshal(body, &googleUser); err != nil {
		return nil, err
	}

	return &googleUser, nil
}

// Verify Apple Identity Token (simplified - in production use Apple's public keys)
func (s *OAuthService) verifyAppleIdentityToken(identityToken string) (*dtos.AppleUserInfo, error) {
	// Parse JWT without verification for now (in production, verify with Apple's public keys)
	token, _, err := new(jwt.Parser).ParseUnverified(identityToken, jwt.MapClaims{})
	if err != nil {
		return nil, err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid Apple identity token claims")
	}

	appleUser := &dtos.AppleUserInfo{
		Sub:   claims["sub"].(string),
		Email: "",
	}

	if email, exists := claims["email"]; exists {
		appleUser.Email = email.(string)
	}

	return appleUser, nil
}

// Create new user from Google OAuth
func (s *OAuthService) createGoogleUser(googleUser *dtos.GoogleUserInfo, req *dtos.GoogleOAuthRequest) (entities.User, error) {
	googleAuthData, _ := json.Marshal(map[string]interface{}{
		"id":               googleUser.ID,
		"email":            googleUser.Email,
		"verified_email":   googleUser.VerifiedEmail,
		"name":             googleUser.Name,
		"given_name":       googleUser.GivenName,
		"family_name":      googleUser.FamilyName,
		"picture":          googleUser.Picture,
		"locale":           googleUser.Locale,
		"access_token":     req.AccessToken,
		"authenticated_at": time.Now().Unix(),
	})

	googleAuthStr := string(googleAuthData)
	provider := "google"

	user := entities.User{
		Base: entities.Base{
			ID: uuid.New(),
		},
		Username:        generateUsernameFromEmail(googleUser.Email),
		Email:           googleUser.Email,
		Name:            googleUser.Name,
		Status:          "active",
		GoogleID:        &googleUser.ID,
		GoogleAuth:      &googleAuthStr,
		OAuthProvider:   &provider,
		ProfileImageURL: &googleUser.Picture,
	}

	if err := s.db.Create(&user).Error; err != nil {
		return entities.User{}, err
	}

	return user, nil
}

// Create new user from Apple OAuth
func (s *OAuthService) createAppleUser(appleUser *dtos.AppleUserInfo, req *dtos.AppleOAuthRequest) (entities.User, error) {
	appleAuthData, _ := json.Marshal(map[string]interface{}{
		"sub":                appleUser.Sub,
		"email":              req.Email,
		"user_identifier":    req.UserIdentifier,
		"identity_token":     req.IdentityToken,
		"authorization_code": req.AuthorizationCode,
		"full_name":          req.FullName,
		"authenticated_at":   time.Now().Unix(),
	})

	appleAuthStr := string(appleAuthData)
	provider := "apple"
	email := req.Email
	if email == "" {
		email = fmt.Sprintf("<EMAIL>", appleUser.Sub)
	}

	name := fmt.Sprintf("%s %s", req.FullName.GivenName, req.FullName.FamilyName)
	if strings.TrimSpace(name) == "" {
		name = "Apple User"
	}

	user := entities.User{
		Base: entities.Base{
			ID: uuid.New(),
		},
		Username:      generateUsernameFromEmail(email),
		Email:         email,
		Name:          name,
		Status:        "active",
		AppleID:       &appleUser.Sub,
		AppleAuth:     &appleAuthStr,
		OAuthProvider: &provider,
	}

	if err := s.db.Create(&user).Error; err != nil {
		return entities.User{}, err
	}

	return user, nil
}

// Link Google account to existing user
func (s *OAuthService) linkGoogleAccount(user *entities.User, googleUser *dtos.GoogleUserInfo, req *dtos.GoogleOAuthRequest) error {
	googleAuthData, _ := json.Marshal(map[string]interface{}{
		"id":             googleUser.ID,
		"email":          googleUser.Email,
		"verified_email": googleUser.VerifiedEmail,
		"name":           googleUser.Name,
		"given_name":     googleUser.GivenName,
		"family_name":    googleUser.FamilyName,
		"picture":        googleUser.Picture,
		"locale":         googleUser.Locale,
		"access_token":   req.AccessToken,
		"linked_at":      time.Now().Unix(),
	})

	googleAuthStr := string(googleAuthData)

	updates := map[string]interface{}{
		"google_id":   googleUser.ID,
		"google_auth": googleAuthStr,
	}

	if user.ProfileImageURL == nil || *user.ProfileImageURL == "" {
		updates["profile_image_url"] = googleUser.Picture
	}

	return s.db.Model(user).Updates(updates).Error
}

// Link Apple account to existing user
func (s *OAuthService) linkAppleAccount(user *entities.User, appleUser *dtos.AppleUserInfo, req *dtos.AppleOAuthRequest) error {
	appleAuthData, _ := json.Marshal(map[string]interface{}{
		"sub":                appleUser.Sub,
		"email":              req.Email,
		"user_identifier":    req.UserIdentifier,
		"identity_token":     req.IdentityToken,
		"authorization_code": req.AuthorizationCode,
		"full_name":          req.FullName,
		"linked_at":          time.Now().Unix(),
	})

	appleAuthStr := string(appleAuthData)

	updates := map[string]interface{}{
		"apple_id":   appleUser.Sub,
		"apple_auth": appleAuthStr,
	}

	return s.db.Model(user).Updates(updates).Error
}

// Generate username from email
func generateUsernameFromEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) > 0 {
		username := strings.ReplaceAll(parts[0], ".", "_")
		return fmt.Sprintf("%s_%d", username, time.Now().Unix()%10000)
	}
	return fmt.Sprintf("user_%d", time.Now().Unix()%10000)
}
