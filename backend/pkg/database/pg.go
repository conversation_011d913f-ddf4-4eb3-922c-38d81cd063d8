package database

import (
	"fmt"
	"log"

	"github.com/NocyTech/fin_notebook/pkg/config"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db  *gorm.DB
	err error
	//client_once sync.Once
)

func InitDB(dbc config.Database) {

	// Print database configuration for debugging
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=Europe/Istanbul", dbc.Host, dbc.Port, dbc.User, dbc.Pass, dbc.Name)
	db, err = gorm.Open(
		postgres.New(
			postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true, // daha az kaynak
			},
		),
	)
	if err != nil {
		panic(err)
	}
	fmt.Println("Successfully connected to the database!")

	db.AutoMigrate(
		&entities.Log{},
		&entities.Version{},
		&entities.User{},
		&entities.Transaction{},
		&entities.Category{},
		&entities.Account{},
		&entities.Budget{},
		&entities.RecurringTransaction{},
		&entities.TokenBlacklist{},
		&entities.BankStatementEntry{},
	)

	SeedData(db)

}

func DBClient() *gorm.DB {
	if db == nil {
		log.Panic("Postgres is not initialized. Call InitDB first.")
	}
	return db
}
