package entities

import (
	"time"

	"github.com/google/uuid"
)

type RecurringTransaction struct {
	Base
	Title         string    `json:"title"`
	Type          string    `json:"type"` // "expense" or "income"
	Amount        float64   `json:"amount"`
	Currency      string    `json:"currency"`
	Interval      string    `json:"interval"` // "daily", "weekly", "monthly", "yearly"
	StartDate     time.Time `json:"start_date"`
	EndDate       time.Time `json:"end_date"`
	CategoryID    uuid.UUID `json:"category_id"`
	PaymentMethod string    `json:"payment_method"`
	AccountID     uuid.UUID `json:"account_id"`
	Note          string    `json:"note"`
	UserID        uuid.UUID `json:"user_id"`
}
