package entities

import "github.com/google/uuid"

type Log struct {
	Base
	Title   string    `json:"title" example:"example title"`
	Message string    `json:"message" example:"order created"`
	Entity  string    `json:"entity" example:"order"`
	Type    string    `json:"type" example:"info"`  // -----> info, error
	Proto   string    `json:"proto" example:"http"` // -----> http, grpc
	Ip      string    `json:"ip" example:"127.0.0.1"`
	UserID  uuid.UUID `json:"user_id" gorm:"default:null;type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}
