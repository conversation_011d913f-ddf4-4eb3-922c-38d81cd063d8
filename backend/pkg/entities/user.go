package entities

type User struct {
	Base
	Username        string  `json:"username"`
	Email           string  `json:"email" gorm:"unique"`
	Password        string  `json:"password"`
	Name            string  `json:"name"`
	LastLogin       string  `json:"last_login"`
	Status          string  `json:"status"`                        // "active", "inactive", "banned"
	GoogleID        *string `json:"google_id" gorm:"unique"`       // Google OAuth ID
	AppleID         *string `json:"apple_id" gorm:"unique"`        // Apple OAuth ID
	GoogleAuth      *string `json:"google_auth" gorm:"type:jsonb"` // Google OAuth data
	AppleAuth       *string `json:"apple_auth" gorm:"type:jsonb"`  // Apple OAuth data
	OAuthProvider   *string `json:"oauth_provider"`                // "google", "apple", "email"
	ProfileImageURL *string `json:"profile_image_url"`             // Profile image from OAuth

	// Guest User Fields
	IsGuest          bool   `json:"is_guest" gorm:"default:false"`      // Whether user is a guest
	GuestID          string `json:"guest_id" gorm:"unique"`             // UUID for guest identification
	Plan             string `json:"plan" gorm:"default:'free'"`         // "guest", "free", "premium"
	TransactionLimit int    `json:"transaction_limit" gorm:"default:0"` // Transaction limit for current plan
	TransactionCount int    `json:"transaction_count" gorm:"default:0"` // Current transaction count
}

// Helper methods for guest user management
func (u *User) IsGuestUser() bool {
	return u.IsGuest
}

func (u *User) CanCreateTransaction() bool {
	if !u.IsGuest {
		return true // Registered users have unlimited transactions
	}
	return u.TransactionCount < u.TransactionLimit
}

func (u *User) GetRemainingTransactions() int {
	if !u.IsGuest {
		return -1 // Unlimited for registered users
	}
	remaining := u.TransactionLimit - u.TransactionCount
	if remaining < 0 {
		return 0
	}
	return remaining
}

func (u *User) IncrementTransactionCount() {
	u.TransactionCount++
}

func (u *User) ConvertToRegistered() {
	u.IsGuest = false
	u.Plan = "free"
	u.TransactionLimit = 0 // Unlimited for registered users
	u.GuestID = ""         // Clear guest ID
}
