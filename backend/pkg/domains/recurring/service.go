package recurring

import (
	"errors"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/domains/account"
	"github.com/NocyTech/fin_notebook/pkg/domains/category"
	"github.com/NocyTech/fin_notebook/pkg/domains/transaction"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/google/uuid"
)

type Service interface {
	CreateRecurringTransaction(userID string, req *dtos.RecurringTransactionRequest) (*dtos.RecurringTransactionResponse, error)
	GetRecurringTransactionByID(id string) (*dtos.RecurringTransactionResponse, error)
	UpdateRecurringTransaction(id string, req *dtos.RecurringTransactionRequest) (*dtos.RecurringTransactionResponse, error)
	DeleteRecurringTransaction(id string) error
	GetAllRecurringTransactions(userID string) ([]dtos.RecurringTransactionResponse, error)
	ProcessDueRecurringTransactions() error
}

type service struct {
	repository         Repository
	categoryService    category.Service
	accountService     account.Service
	transactionService transaction.Service
}

func NewService(r Repository, cs category.Service, as account.Service, ts transaction.Service) Service {
	return &service{
		repository:         r,
		categoryService:    cs,
		accountService:     as,
		transactionService: ts,
	}
}

func (s *service) CreateRecurringTransaction(userID string, req *dtos.RecurringTransactionRequest) (*dtos.RecurringTransactionResponse, error) {
	// Parse UUIDs
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	categoryUUID, err := uuid.Parse(req.CategoryID)
	if err != nil {
		return nil, errors.New("invalid category ID")
	}

	accountUUID, err := uuid.Parse(req.AccountID)
	if err != nil {
		return nil, errors.New("invalid account ID")
	}

	// Create recurring transaction entity
	recurring := &entities.RecurringTransaction{
		Title:         req.Title,
		Type:          req.Type,
		Amount:        req.Amount,
		Currency:      req.Currency,
		Interval:      req.Interval,
		StartDate:     req.StartDate,
		EndDate:       req.EndDate,
		CategoryID:    categoryUUID,
		PaymentMethod: req.PaymentMethod,
		AccountID:     accountUUID,
		Note:          req.Note,
		UserID:        userUUID,
	}

	// Save recurring transaction
	if err := s.repository.Create(recurring); err != nil {
		return nil, err
	}

	// Get recurring transaction with relations
	savedRecurring, err := s.repository.FindByID(recurring.ID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.toRecurringTransactionResponse(savedRecurring), nil
}

func (s *service) GetRecurringTransactionByID(id string) (*dtos.RecurringTransactionResponse, error) {
	// Parse UUID
	recurringUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid recurring transaction ID")
	}

	// Get recurring transaction
	recurring, err := s.repository.FindByID(recurringUUID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.toRecurringTransactionResponse(recurring), nil
}

func (s *service) UpdateRecurringTransaction(id string, req *dtos.RecurringTransactionRequest) (*dtos.RecurringTransactionResponse, error) {
	// Parse UUID
	recurringUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid recurring transaction ID")
	}

	// Get recurring transaction
	recurring, err := s.repository.FindByID(recurringUUID)
	if err != nil {
		return nil, err
	}

	// Update recurring transaction fields
	if req.Title != "" {
		recurring.Title = req.Title
	}
	if req.Type != "" {
		recurring.Type = req.Type
	}
	if req.Amount > 0 {
		recurring.Amount = req.Amount
	}
	if req.Currency != "" {
		recurring.Currency = req.Currency
	}
	if req.Interval != "" {
		recurring.Interval = req.Interval
	}
	if !req.StartDate.IsZero() {
		recurring.StartDate = req.StartDate
	}
	// EndDate can be zero (null)
	recurring.EndDate = req.EndDate
	if req.CategoryID != "" {
		categoryUUID, err := uuid.Parse(req.CategoryID)
		if err != nil {
			return nil, errors.New("invalid category ID")
		}
		recurring.CategoryID = categoryUUID
	}
	if req.PaymentMethod != "" {
		recurring.PaymentMethod = req.PaymentMethod
	}
	if req.AccountID != "" {
		accountUUID, err := uuid.Parse(req.AccountID)
		if err != nil {
			return nil, errors.New("invalid account ID")
		}
		recurring.AccountID = accountUUID
	}
	if req.Note != "" {
		recurring.Note = req.Note
	}

	// Save recurring transaction
	if err := s.repository.Update(recurring); err != nil {
		return nil, err
	}

	// Get updated recurring transaction
	updatedRecurring, err := s.repository.FindByID(recurring.ID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.toRecurringTransactionResponse(updatedRecurring), nil
}

func (s *service) DeleteRecurringTransaction(id string) error {
	// Parse UUID
	recurringUUID, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid recurring transaction ID")
	}

	// Delete recurring transaction
	return s.repository.Delete(recurringUUID)
}

func (s *service) GetAllRecurringTransactions(userID string) ([]dtos.RecurringTransactionResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Get recurring transactions
	recurrings, err := s.repository.FindAll(userUUID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	var response []dtos.RecurringTransactionResponse
	for _, recurring := range recurrings {
		response = append(response, *s.toRecurringTransactionResponse(&recurring))
	}

	return response, nil
}

func (s *service) ProcessDueRecurringTransactions() error {
	// Get today's date
	today := time.Now()

	// Get due recurring transactions
	dueRecurrings, err := s.repository.FindDueRecurringTransactions(today)
	if err != nil {
		return err
	}

	// Process each due recurring transaction
	for _, recurring := range dueRecurrings {
		// Create transaction from recurring transaction
		transactionReq := &dtos.TransactionRequest{
			Title:           recurring.Title,
			Type:            recurring.Type, // Use the type from recurring transaction
			Amount:          recurring.Amount,
			Currency:        recurring.Currency,
			CategoryID:      recurring.CategoryID.String(),
			PaymentMethod:   recurring.PaymentMethod,
			AccountID:       recurring.AccountID.String(),
			Note:            recurring.Note + " (Recurring)",
			TransactionDate: today,
			Location:        "",
		}

		// Create transaction
		_, err := s.transactionService.CreateTransaction(recurring.UserID.String(), transactionReq)
		if err != nil {
			// Log error but continue processing other recurring transactions
			continue
		}
	}

	return nil
}

func (s *service) toRecurringTransactionResponse(recurring *entities.RecurringTransaction) *dtos.RecurringTransactionResponse {
	// Get category separately
	category, err := s.categoryService.GetCategoryByID(recurring.CategoryID.String())
	if err != nil {
		// If category not found, use empty category
		category = &dtos.CategoryResponse{
			ID:   recurring.CategoryID.String(),
			Name: "Unknown Category",
			Type: "expense",
			Icon: "help",
		}
	}

	// Get account separately
	account, err := s.accountService.GetAccountByID(recurring.AccountID.String())
	if err != nil {
		// If account not found, use empty account
		account = &dtos.AccountResponse{
			ID:       recurring.AccountID.String(),
			Name:     "Unknown Account",
			Type:     "cash",
			Balance:  0,
			Currency: "TRY",
		}
	}

	return &dtos.RecurringTransactionResponse{
		ID:         recurring.ID.String(),
		Title:      recurring.Title,
		Type:       recurring.Type,
		Amount:     recurring.Amount,
		Currency:   recurring.Currency,
		Interval:   recurring.Interval,
		StartDate:  recurring.StartDate,
		EndDate:    recurring.EndDate,
		CategoryID: recurring.CategoryID.String(),
		Category: dtos.CategoryDTO{
			ID:   category.ID,
			Name: category.Name,
			Type: category.Type,
			Icon: category.Icon,
		},
		PaymentMethod: recurring.PaymentMethod,
		AccountID:     recurring.AccountID.String(),
		Account: dtos.AccountDTO{
			ID:       account.ID,
			Name:     account.Name,
			Type:     account.Type,
			Balance:  account.Balance,
			Currency: account.Currency,
		},
		Note:      recurring.Note,
		CreatedAt: recurring.CreatedAt,
		UpdatedAt: recurring.UpdatedAt,
	}
}
