package recurring

import (
	"errors"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	Create(recurring *entities.RecurringTransaction) error
	FindByID(id uuid.UUID) (*entities.RecurringTransaction, error)
	Update(recurring *entities.RecurringTransaction) error
	Delete(id uuid.UUID) error
	FindAll(userID uuid.UUID) ([]entities.RecurringTransaction, error)
	FindDueRecurringTransactions(date time.Time) ([]entities.RecurringTransaction, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(recurring *entities.RecurringTransaction) error {
	return r.db.Create(recurring).Error
}

func (r *repository) FindByID(id uuid.UUID) (*entities.RecurringTransaction, error) {
	var recurring entities.RecurringTransaction
	if err := r.db.Where("id = ?", id).First(&recurring).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("recurring transaction not found")
		}
		return nil, err
	}
	return &recurring, nil
}

func (r *repository) Update(recurring *entities.RecurringTransaction) error {
	return r.db.Save(recurring).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.RecurringTransaction{}, "id = ?", id).Error
}

func (r *repository) FindAll(userID uuid.UUID) ([]entities.RecurringTransaction, error) {
	var recurrings []entities.RecurringTransaction
	if err := r.db.Preload("Category").Preload("Account").Where("user_id = ?", userID).Find(&recurrings).Error; err != nil {
		return nil, err
	}
	return recurrings, nil
}

func (r *repository) FindDueRecurringTransactions(date time.Time) ([]entities.RecurringTransaction, error) {
	var recurrings []entities.RecurringTransaction

	// Find all recurring transactions that are due today
	if err := r.db.Preload("Category").Preload("Account").
		Where("start_date <= ? AND (end_date IS NULL OR end_date >= ?)", date, date).
		Find(&recurrings).Error; err != nil {
		return nil, err
	}

	// Filter out transactions that are not due today based on their interval
	var dueRecurrings []entities.RecurringTransaction
	for _, recurring := range recurrings {
		if isDue(recurring, date) {
			dueRecurrings = append(dueRecurrings, recurring)
		}
	}

	return dueRecurrings, nil
}

// isDue checks if a recurring transaction is due on the given date
func isDue(recurring entities.RecurringTransaction, date time.Time) bool {
	startDate := recurring.StartDate

	switch recurring.Interval {
	case "daily":
		return true
	case "weekly":
		daysDiff := int(date.Sub(startDate).Hours() / 24)
		return daysDiff%7 == 0
	case "monthly":
		// Due on the same day of the month, with special handling for month-end dates
		return isMonthlyDue(startDate, date)
	case "yearly":
		// Due on the same day and month
		return date.Day() == startDate.Day() && date.Month() == startDate.Month()
	default:
		return false
	}
}

// isMonthlyDue checks if a monthly recurring transaction is due on the given date
// Handles edge cases like month-end dates (e.g., if started on 31st, runs on last day of each month)
func isMonthlyDue(startDate, currentDate time.Time) bool {
	startDay := startDate.Day()
	currentDay := currentDate.Day()

	// If the start day matches current day, it's due
	if startDay == currentDay {
		return true
	}

	// Handle month-end scenarios
	// Get the last day of the current month
	year, month, _ := currentDate.Date()
	lastDayOfMonth := time.Date(year, month+1, 0, 0, 0, 0, 0, currentDate.Location()).Day()

	// If start date was on the last day of its month, and today is the last day of current month
	startYear, startMonth, _ := startDate.Date()
	lastDayOfStartMonth := time.Date(startYear, startMonth+1, 0, 0, 0, 0, 0, startDate.Location()).Day()

	if startDay == lastDayOfStartMonth && currentDay == lastDayOfMonth {
		return true
	}

	// If start day is greater than current month's last day, run on last day of current month
	if startDay > lastDayOfMonth && currentDay == lastDayOfMonth {
		return true
	}

	return false
}
