package bank_statement

import (
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Repository defines the interface for bank statement operations
type Repository interface {
	SaveBankStatementEntry(entry *entities.BankStatementEntry) error
	FindBankStatementEntriesByUserID(userID uuid.UUID) ([]entities.BankStatementEntry, error)
}

// repository implements the Repository interface
type repository struct {
	db *gorm.DB
}

// NewRepo creates a new bank statement repository
func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// SaveBankStatementEntry saves a bank statement entry to the database
func (r *repository) SaveBankStatementEntry(entry *entities.BankStatementEntry) error {
	return r.db.Create(entry).Error
}

// FindBankStatementEntriesByUserID finds all bank statement entries for a user
func (r *repository) FindBankStatementEntriesByUserID(userID uuid.UUID) ([]entities.BankStatementEntry, error) {
	var entries []entities.BankStatementEntry
	if err := r.db.Where("user_id = ?", userID).Find(&entries).Error; err != nil {
		return nil, err
	}
	return entries, nil
}
