package report

import (
	"fmt"
	"sort"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	GetSummary(userID uuid.UUID, startDate, endDate time.Time) (*dtos.SummaryReportResponse, error)
	GetCategoryBreakdown(userID uuid.UUID, startDate, endDate time.Time) (*dtos.CategoryBreakdownResponse, error)
	GetMonthlyReport(userID uuid.UUID, year int) (*dtos.MonthlyReportResponse, error)
	GetLocationSummary(userID uuid.UUID, startDate, endDate time.Time) (*dtos.LocationSummaryResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) GetSummary(userID uuid.UUID, startDate, endDate time.Time) (*dtos.SummaryReportResponse, error) {
	var totalIncome, totalExpense float64
	var totalTransactions int64

	// Get total income
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "income", startDate, endDate).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&totalIncome).Error; err != nil {
		return nil, err
	}

	// Get total expense
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "expense", startDate, endDate).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&totalExpense).Error; err != nil {
		return nil, err
	}

	// Get total transaction count
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND transaction_date BETWEEN ? AND ?", userID, startDate, endDate).
		Count(&totalTransactions).Error; err != nil {
		return nil, err
	}

	// Calculate current month's income and expenses
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthEnd := monthStart.AddDate(0, 1, -1)

	var monthlyIncome, monthlyExpenses float64
	var monthlyTransactions int64

	// Get current month income
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "income", monthStart, monthEnd).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&monthlyIncome).Error; err != nil {
		return nil, err
	}

	// Get current month expenses
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "expense", monthStart, monthEnd).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&monthlyExpenses).Error; err != nil {
		return nil, err
	}

	// Get current month transaction count
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND transaction_date BETWEEN ? AND ?", userID, monthStart, monthEnd).
		Count(&monthlyTransactions).Error; err != nil {
		return nil, err
	}

	// Calculate previous month's data for comparison
	prevMonthStart := monthStart.AddDate(0, -1, 0)
	prevMonthEnd := prevMonthStart.AddDate(0, 1, -1)

	var prevMonthlyIncome, prevMonthlyExpenses float64
	var prevMonthlyTransactions int64

	// Get previous month income
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "income", prevMonthStart, prevMonthEnd).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&prevMonthlyIncome).Error; err != nil {
		return nil, err
	}

	// Get previous month expenses
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "expense", prevMonthStart, prevMonthEnd).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&prevMonthlyExpenses).Error; err != nil {
		return nil, err
	}

	// Get previous month transaction count
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND transaction_date BETWEEN ? AND ?", userID, prevMonthStart, prevMonthEnd).
		Count(&prevMonthlyTransactions).Error; err != nil {
		return nil, err
	}

	// Get all categories for the user
	var categories []entities.Category
	if err := r.db.Where("user_id = ?", userID).Find(&categories).Error; err != nil {
		return nil, err
	}

	// Create a map for quick category lookup
	categoryMap := make(map[uuid.UUID]string)
	for _, category := range categories {
		categoryMap[category.ID] = category.Name
	}

	// Get all transactions for the date range
	var transactions []entities.Transaction
	if err := r.db.Where("user_id = ? AND transaction_date BETWEEN ? AND ?", userID, startDate, endDate).Find(&transactions).Error; err != nil {
		return nil, err
	}

	// Calculate category amounts
	expenseCategoryAmounts := make(map[uuid.UUID]float64)
	incomeCategoryAmounts := make(map[uuid.UUID]float64)

	for _, transaction := range transactions {
		if transaction.Type == "expense" {
			expenseCategoryAmounts[transaction.CategoryID] += transaction.Amount
		} else if transaction.Type == "income" {
			incomeCategoryAmounts[transaction.CategoryID] += transaction.Amount
		}
	}

	// Convert to sorted slices and get top 5
	type CategoryAmount struct {
		CategoryID   uuid.UUID
		CategoryName string
		Amount       float64
	}

	var topExpenseCategories []CategoryAmount
	for categoryID, amount := range expenseCategoryAmounts {
		if categoryName, exists := categoryMap[categoryID]; exists {
			topExpenseCategories = append(topExpenseCategories, CategoryAmount{
				CategoryID:   categoryID,
				CategoryName: categoryName,
				Amount:       amount,
			})
		}
	}

	var topIncomeCategories []CategoryAmount
	for categoryID, amount := range incomeCategoryAmounts {
		if categoryName, exists := categoryMap[categoryID]; exists {
			topIncomeCategories = append(topIncomeCategories, CategoryAmount{
				CategoryID:   categoryID,
				CategoryName: categoryName,
				Amount:       amount,
			})
		}
	}

	// Sort by amount (descending) and take top 5
	sort.Slice(topExpenseCategories, func(i, j int) bool {
		return topExpenseCategories[i].Amount > topExpenseCategories[j].Amount
	})
	if len(topExpenseCategories) > 5 {
		topExpenseCategories = topExpenseCategories[:5]
	}

	sort.Slice(topIncomeCategories, func(i, j int) bool {
		return topIncomeCategories[i].Amount > topIncomeCategories[j].Amount
	})
	if len(topIncomeCategories) > 5 {
		topIncomeCategories = topIncomeCategories[:5]
	}

	// Calculate percentage changes
	balanceChange := r.calculatePercentageChange(monthlyIncome-monthlyExpenses, prevMonthlyIncome-prevMonthlyExpenses)
	incomeChange := r.calculatePercentageChange(monthlyIncome, prevMonthlyIncome)
	expenseChange := r.calculatePercentageChange(monthlyExpenses, prevMonthlyExpenses)
	transactionChange := r.calculateTransactionChange(int(monthlyTransactions), int(prevMonthlyTransactions))

	// Convert to response
	response := &dtos.SummaryReportResponse{
		TotalIncome:       totalIncome,
		TotalExpense:      totalExpense,
		NetBalance:        totalIncome - totalExpense,
		TotalBalance:      totalIncome - totalExpense, // Same as net balance for now
		MonthlyIncome:     monthlyIncome,
		MonthlyExpenses:   monthlyExpenses,
		TotalTransactions: int(totalTransactions),
		BalanceChange:     balanceChange,
		IncomeChange:      incomeChange,
		ExpenseChange:     expenseChange,
		TransactionChange: transactionChange,
	}

	for _, category := range topExpenseCategories {
		response.TopExpenseCategories = append(response.TopExpenseCategories, dtos.CategoryAmountDTO{
			Category: category.CategoryName,
			Amount:   category.Amount,
		})
	}

	for _, category := range topIncomeCategories {
		response.TopIncomeCategories = append(response.TopIncomeCategories, dtos.CategoryAmountDTO{
			Category: category.CategoryName,
			Amount:   category.Amount,
		})
	}

	return response, nil
}

func (r *repository) GetCategoryBreakdown(userID uuid.UUID, startDate, endDate time.Time) (*dtos.CategoryBreakdownResponse, error) {
	// Get all categories for the user
	var categories []entities.Category
	if err := r.db.Where("user_id = ?", userID).Find(&categories).Error; err != nil {
		return nil, err
	}

	// Create a map for quick category lookup
	categoryMap := make(map[uuid.UUID]string)
	for _, category := range categories {
		categoryMap[category.ID] = category.Name
	}

	// Get all transactions for the date range
	var transactions []entities.Transaction
	if err := r.db.Where("user_id = ? AND transaction_date BETWEEN ? AND ?", userID, startDate, endDate).Find(&transactions).Error; err != nil {
		return nil, err
	}

	// Calculate category amounts
	expenseCategoryAmounts := make(map[uuid.UUID]float64)
	incomeCategoryAmounts := make(map[uuid.UUID]float64)

	for _, transaction := range transactions {
		if transaction.Type == "expense" {
			expenseCategoryAmounts[transaction.CategoryID] += transaction.Amount
		} else if transaction.Type == "income" {
			incomeCategoryAmounts[transaction.CategoryID] += transaction.Amount
		}
	}

	// Convert to response
	response := &dtos.CategoryBreakdownResponse{}

	// Add expense categories
	for categoryID, amount := range expenseCategoryAmounts {
		if categoryName, exists := categoryMap[categoryID]; exists {
			response.ExpenseCategories = append(response.ExpenseCategories, dtos.CategoryAmountDTO{
				Category: categoryName,
				Amount:   amount,
			})
		}
	}

	// Add income categories
	for categoryID, amount := range incomeCategoryAmounts {
		if categoryName, exists := categoryMap[categoryID]; exists {
			response.IncomeCategories = append(response.IncomeCategories, dtos.CategoryAmountDTO{
				Category: categoryName,
				Amount:   amount,
			})
		}
	}

	// Sort by amount (descending)
	sort.Slice(response.ExpenseCategories, func(i, j int) bool {
		return response.ExpenseCategories[i].Amount > response.ExpenseCategories[j].Amount
	})

	sort.Slice(response.IncomeCategories, func(i, j int) bool {
		return response.IncomeCategories[i].Amount > response.IncomeCategories[j].Amount
	})

	return response, nil
}

func (r *repository) GetMonthlyReport(userID uuid.UUID, year int) (*dtos.MonthlyReportResponse, error) {
	type MonthlyData struct {
		Month   int
		Income  float64
		Expense float64
	}

	var monthlyData []MonthlyData

	// Get monthly income and expense
	if err := r.db.Model(&entities.Transaction{}).
		Select("EXTRACT(MONTH FROM transaction_date) as month, "+
			"COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as income, "+
			"COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as expense").
		Where("user_id = ? AND EXTRACT(YEAR FROM transaction_date) = ?", userID, year).
		Group("month").
		Order("month").
		Scan(&monthlyData).Error; err != nil {
		return nil, err
	}

	// Convert to response
	response := &dtos.MonthlyReportResponse{}

	// Ensure all months are included
	monthNames := []string{"January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"}
	monthDataMap := make(map[int]MonthlyData)

	for _, data := range monthlyData {
		monthDataMap[data.Month] = data
	}

	for i := 1; i <= 12; i++ {
		data, exists := monthDataMap[i]
		if !exists {
			data = MonthlyData{Month: i, Income: 0, Expense: 0}
		}

		response.Months = append(response.Months, dtos.MonthlyDataDTO{
			Month:   monthNames[i-1],
			Income:  data.Income,
			Expense: data.Expense,
		})
	}

	return response, nil
}

func (r *repository) GetLocationSummary(userID uuid.UUID, startDate, endDate time.Time) (*dtos.LocationSummaryResponse, error) {
	type LocationAmount struct {
		Location string
		Amount   float64
	}

	var locationAmounts []LocationAmount

	// Get location summary
	if err := r.db.Model(&entities.Transaction{}).
		Select("location, COALESCE(SUM(amount), 0) as amount").
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ? AND location != ''", userID, "expense", startDate, endDate).
		Group("location").
		Order("amount DESC").
		Scan(&locationAmounts).Error; err != nil {
		return nil, err
	}

	// Convert to response
	response := &dtos.LocationSummaryResponse{}

	for _, location := range locationAmounts {
		response.Locations = append(response.Locations, dtos.LocationAmountDTO{
			Location: location.Location,
			Amount:   location.Amount,
		})
	}

	return response, nil
}

// calculatePercentageChange calculates the percentage change between current and previous values
func (r *repository) calculatePercentageChange(current, previous float64) string {
	if previous == 0 {
		if current > 0 {
			return "+100.0%"
		} else if current < 0 {
			return "-100.0%"
		}
		return "+0.0%"
	}

	change := ((current - previous) / previous) * 100
	if change >= 0 {
		return fmt.Sprintf("+%.1f%%", change)
	}
	return fmt.Sprintf("%.1f%%", change)
}

// calculateTransactionChange calculates the change in transaction count
func (r *repository) calculateTransactionChange(current, previous int) string {
	change := current - previous
	if change >= 0 {
		return fmt.Sprintf("+%d", change)
	}
	return fmt.Sprintf("%d", change)
}
