package budget

import (
	"errors"

	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/google/uuid"
)

type Service interface {
	CreateBudget(userID string, req dtos.BudgetRequest) error
	UpdateBudget(userID string, id string, req dtos.BudgetRequest) error
	DeleteBudget(userID string, id string) error
	GetAllBudgets(userId string) ([]dtos.BudgetResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateBudget(userID string, req dtos.BudgetRequest) error {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return errors.New("invalid user ID")
	}

	categoryUUID, err := uuid.Parse(req.CategoryId)
	if err != nil {
		return errors.New("invalid category ID")
	}

	budget := &entities.Budget{
		Name:       req.Name,
		Amount:     req.Amount,
		Period:     req.Period,
		CategoryId: categoryUUID,
		UserId:     userUUID,
		Color:      req.Color,
	}

	if err := s.repository.Create(budget); err != nil {
		return err
	}

	return nil
}

func (s *service) UpdateBudget(userID string, id string, req dtos.BudgetRequest) error {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return errors.New("invalid user ID")
	}

	budgetUUID, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid budget ID")
	}

	if err := s.repository.UpdateWithUserCheck(userUUID, budgetUUID, req); err != nil {
		return err
	}
	return nil
}

func (s *service) DeleteBudget(userID string, id string) error {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return errors.New("invalid user ID")
	}

	budgetUUID, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid budget ID")
	}

	if err := s.repository.DeleteWithUserCheck(userUUID, budgetUUID); err != nil {
		return err
	}
	return nil
}

func (s *service) GetAllBudgets(userID string) ([]dtos.BudgetResponse, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}
	return s.repository.FindAll(userUUID)
}
