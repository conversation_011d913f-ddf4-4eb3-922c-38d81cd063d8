app:
  name: fin_notebook
  port: 8008
  host:
  jwt_issuer: "fin_notebook"
  jwt_secret: "secret"
  jwt_expire: 24
  client_id: xxx
  onesignal_api_key: 123456
  force_update_key: xxx
google_oauth:
  client_id: "your-google-client-id"
  client_secret: "your-google-client-secret"
  redirect_url: "http://localhost:8008/api/v1/auth/google/callback"
database:
  host: fin_notebook-db
  port: 5432
  user: fin_notebook-user
  pass: fin_notebook-pass
  name: fin_notebook
cloudinary:
  name: xxx
  api_key: xxx
  api_secret: xxx
  api_folder: xxx
allows:
  methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS
  headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  origins:
    - http://*********:3001
    - http://localhost:3000
    - http://localhost:3001
    - http://localhost:8008
    - https://app.butce360.com
    - https://butce360.com
