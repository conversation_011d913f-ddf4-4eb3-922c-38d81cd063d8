import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Eye, EyeOff, BarChart3, Shield, TrendingUp} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { authAPI } from '../../services/api';
import logoImage from '../../assets/logo-butce360.png';
import LanguageSwitcher from '../common/LanguageSwitcher';

// Butce360 Logo Component
const Butce360Logo: React.FC<{ className?: string; size?: 'sm' | 'md' | 'lg' }> = ({
  className = "",
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'h-14 w-full max-w-none',
    md: 'h-18 w-full max-w-none',
    lg: 'h-20 w-full max-w-none'
  };

  return (
    <div className={`flex items-center justify-center w-full h-full p-2 ${className}`}>
      <img
        src={logoImage}
        alt="Bütçe360 Logo"
        className={`${sizeClasses[size]} object-contain`}
        style={{ maxWidth: 'none', width: '100%' }}
      />
    </div>
  );
};

const Login: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { login, isAuthenticated } = useAuth();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await login(formData.username, formData.password);
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Login error:', error);
      setError(error.message || 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = () => {
    // Redirect to Google OAuth endpoint
    authAPI.googleLogin();
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-slate-900 relative overflow-hidden">
      {/* Language Switcher */}
      <div className="absolute top-4 right-4 z-30">
        <LanguageSwitcher />
      </div>

      {/* Background decorative elements */}
      <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-br from-pink-500/10 to-orange-500/10 rounded-full blur-3xl"></div>

      {/* Left Panel - Login Form - Compact */}
      <div className="flex-1 lg:flex-[2] flex items-center justify-center p-4 relative z-10 order-2 lg:order-1">
        <div className="w-full max-w-md">
          {/* Logo and Header - Compact */}
          <div className="mb-6">
            <div className="flex items-center justify-center mb-4">
              <Butce360Logo size="md" className="max-w-xs" />
            </div>
          </div>

          {/* Social Login Buttons - Compact */}
          <div className="space-y-2 mb-4">
            <button
              onClick={handleGoogleLogin}
              className="w-full flex items-center justify-center px-3 py-2.5 bg-white hover:bg-gray-50 text-gray-700 rounded-lg font-medium transition-all duration-200 shadow-md text-sm"
            >
              <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              {t('auth.loginWithGoogle')}
            </button>
          </div>

          {/* Divider - Compact */}
          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-slate-700"></div>
            </div>
            <div className="relative flex justify-center text-xs">
              <span className="px-3 bg-slate-900 text-slate-400">{t('auth.orLoginWithEmail')}</span>
            </div>
          </div>

          {error && (
            <div className="mb-4 p-3 rounded-lg bg-red-900/20 border border-red-800/30">
              <p className="text-xs text-red-300">{error}</p>
            </div>
          )}

          {/* Login Form - Compact */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-xs font-medium text-slate-300 mb-1">
                {t('auth.username')}
              </label>
              <input
                name="username"
                type="text"
                required
                value={formData.username}
                onChange={handleChange}
                className="w-full px-3 py-2.5 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 transition-all text-sm"
                placeholder={t('auth.enterUsername')}
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-slate-300 mb-1">
                {t('auth.password')}
              </label>
              <div className="relative">
                <input
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full px-3 py-2.5 pr-10 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 transition-all text-sm"
                  placeholder={t('auth.enterPassword')}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-300"
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="w-3 h-3 text-indigo-600 bg-slate-800 border-slate-600 rounded focus:ring-indigo-500"
                />
                <span className="ml-2 text-xs text-slate-300">{t('auth.rememberMe')}</span>
              </label>
              <button type="button" className="text-xs text-indigo-400 hover:text-indigo-300">
                {t('auth.forgotPassword')}
              </button>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full py-3 bg-gradient-to-r from-rose-200 to-rose-100 hover:from-rose-300 hover:to-rose-200 text-slate-800 font-semibold rounded-lg transition-all duration-200 shadow-lg disabled:opacity-50 text-sm"
            >
              {loading ? t('auth.loggingIn') : t('auth.login')}
            </button>
          </form>

          <div className="mt-4 text-center">
            <p className="text-slate-400 text-xs">
              {t('auth.noAccount')}{' '}
              <Link to="/register" className="text-indigo-400 hover:text-indigo-300 font-medium">
                {t('auth.register')}
              </Link>
            </p>
          </div>

          {/* Mobile Dashboard Preview */}
          <div className="lg:hidden mt-6 bg-slate-800/50 rounded-xl p-3 border border-slate-700/50 backdrop-blur-sm">
            <div className="mb-3">
              <h4 className="text-white font-medium text-xs mb-1">{t('dashboard.preview')}</h4>
            </div>

            {/* Mini Stats Cards */}
            <div className="grid grid-cols-2 gap-2 mb-3">
              <div className="bg-slate-900/60 rounded-lg p-2 border border-slate-600/30">
                <div className="flex flex-col">
                  <span className="text-slate-400 text-xs">{t('dashboard.totalBalance')}</span>
                  <div className="text-xs font-bold text-white">₺9,820.54</div>
                  <span className="text-red-400 text-xs">-100.0%</span>
                </div>
              </div>

              <div className="bg-slate-900/60 rounded-lg p-2 border border-slate-600/30">
                <div className="flex flex-col">
                  <span className="text-slate-400 text-xs">{t('dashboard.totalTransactions')}</span>
                  <div className="text-xs font-bold text-white">58</div>
                  <span className="text-red-400 text-xs">-16</span>
                </div>
              </div>

              <div className="bg-slate-900/60 rounded-lg p-2 border border-slate-600/30">
                <div className="flex flex-col">
                  <span className="text-slate-400 text-xs">{t('dashboard.monthlyIncome')}</span>
                  <div className="text-xs font-bold text-white">₺0.00</div>
                  <span className="text-red-400 text-xs">-100.0%</span>
                </div>
              </div>

              <div className="bg-slate-900/60 rounded-lg p-2 border border-slate-600/30">
                <div className="flex flex-col">
                  <span className="text-slate-400 text-xs">{t('dashboard.monthlyExpense')}</span>
                  <div className="text-xs font-bold text-white">₺0.00</div>
                  <span className="text-red-400 text-xs">-100.0%</span>
                </div>
              </div>
            </div>

            {/* Mini Recent Transactions */}
            <div className="bg-slate-900/60 rounded-lg p-2 border border-slate-600/30">
              <h5 className="text-white text-xs font-medium mb-2">{t('dashboard.recentTransactions')}</h5>
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-white text-xs">Restaurant Dinner</div>
                    <div className="text-slate-400 text-xs">Dining Out • 5/25/2025</div>
                  </div>
                  <div className="text-red-400 text-xs">-₺300.00</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-white text-xs">Weekly Groceries</div>
                    <div className="text-slate-400 text-xs">Groceries • 5/24/2025</div>
                  </div>
                  <div className="text-red-400 text-xs">-₺500.00</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-white text-xs">Freelance Project</div>
                    <div className="text-slate-400 text-xs">Freelance • 5/20/2025</div>
                  </div>
                  <div className="text-green-400 text-xs">+₺2,500.00</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Panel - Features & Preview - Minimalist */}
      <div className="flex-1 bg-slate-800 p-2 lg:p-4 flex flex-col justify-center relative overflow-hidden order-1 lg:order-2 min-h-[15vh] lg:min-h-screen">
        {/* Background decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-teal-500/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-blue-500/10 rounded-full blur-xl"></div>
        
        <div className="relative z-10 max-w-sm mx-auto w-full">
          {/* Features Section - Minimalist */}
          <div className="mb-2 lg:mb-6">
            <div className="space-y-1 lg:space-y-4">
              <div className="flex items-center space-x-2 lg:space-x-3">
                <div className="w-4 h-4 lg:w-8 lg:h-8 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-2 h-2 lg:w-4 lg:h-4 text-indigo-400" />
                </div>
                <div>
                  <h3 className="text-white font-medium text-xs lg:text-sm">{t('features.incomeExpenseTracking')}</h3>
                </div>
              </div>

              <div className="flex items-center space-x-2 lg:space-x-3">
                <div className="w-4 h-4 lg:w-8 lg:h-8 bg-gradient-to-br from-teal-500/20 to-blue-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <TrendingUp className="w-2 h-2 lg:w-4 lg:h-4 text-teal-400" />
                </div>
                <div>
                  <h3 className="text-white font-medium text-xs lg:text-sm">{t('features.smartReporting')}</h3>
                </div>
              </div>

              <div className="flex items-center space-x-2 lg:space-x-3">
                <div className="w-4 h-4 lg:w-8 lg:h-8 bg-gradient-to-br from-rose-500/20 to-orange-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Shield className="w-2 h-2 lg:w-4 lg:h-4 text-rose-400" />
                </div>
                <div>
                  <h3 className="text-white font-medium text-xs lg:text-sm">{t('features.securePrivate')}</h3>
                </div>
              </div>
            </div>
          </div>

          {/* Mini Dashboard Preview - Desktop Only */}
          <div className="hidden lg:block bg-slate-900/50 rounded-xl p-3 lg:p-4 border border-slate-700/50 backdrop-blur-sm">
            <div className="mb-3">
              <h4 className="text-white font-medium text-xs mb-1">{t('dashboard.preview')}</h4>
            </div>

            {/* Mini Stats Cards */}
            <div className="grid grid-cols-2 gap-2 mb-3">
              <div className="bg-slate-800/60 rounded-lg p-2 border border-slate-600/30">
                <div className="flex flex-col">
                  <span className="text-slate-400 text-xs">{t('dashboard.totalBalance')}</span>
                  <div className="text-xs font-bold text-white">₺9,820.54</div>
                  <span className="text-red-400 text-xs">-100.0%</span>
                </div>
              </div>

              <div className="bg-slate-800/60 rounded-lg p-2 border border-slate-600/30">
                <div className="flex flex-col">
                  <span className="text-slate-400 text-xs">{t('dashboard.totalTransactions')}</span>
                  <div className="text-xs font-bold text-white">58</div>
                  <span className="text-red-400 text-xs">-16</span>
                </div>
              </div>

              <div className="bg-slate-800/60 rounded-lg p-2 border border-slate-600/30">
                <div className="flex flex-col">
                  <span className="text-slate-400 text-xs">{t('dashboard.monthlyIncome')}</span>
                  <div className="text-xs font-bold text-white">₺0.00</div>
                  <span className="text-red-400 text-xs">-100.0%</span>
                </div>
              </div>

              <div className="bg-slate-800/60 rounded-lg p-2 border border-slate-600/30">
                <div className="flex flex-col">
                  <span className="text-slate-400 text-xs">{t('dashboard.monthlyExpense')}</span>
                  <div className="text-xs font-bold text-white">₺0.00</div>
                  <span className="text-red-400 text-xs">-100.0%</span>
                </div>
              </div>
            </div>

            {/* Mini Recent Transactions */}
            <div className="bg-slate-800/60 rounded-lg p-3 border border-slate-600/30">
              <h5 className="text-white text-xs font-medium mb-2">{t('dashboard.recentTransactions')}</h5>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-white text-xs">Restaurant Dinner</div>
                    <div className="text-slate-400 text-xs">Dining Out • 5/25/2025</div>
                  </div>
                  <div className="text-red-400 text-xs">-₺300.00</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-white text-xs">Weekly Groceries</div>
                    <div className="text-slate-400 text-xs">Groceries • 5/24/2025</div>
                  </div>
                  <div className="text-red-400 text-xs">-₺500.00</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-white text-xs">Freelance Project</div>
                    <div className="text-slate-400 text-xs">Freelance • 5/20/2025</div>
                  </div>
                  <div className="text-green-400 text-xs">+₺2,500.00</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;