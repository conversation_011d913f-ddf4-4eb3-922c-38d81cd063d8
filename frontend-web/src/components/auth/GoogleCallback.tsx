import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const GoogleCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { setAuthFromCallback } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const errorParam = searchParams.get('error');
        const token = searchParams.get('token');
        const userId = searchParams.get('user_id');
        const username = searchParams.get('username');
        const email = searchParams.get('email');
        const name = searchParams.get('name');

        if (errorParam) {
          setError(`Google authentication failed: ${errorParam}`);
          setLoading(false);
          return;
        }

        if (!token || !userId || !email || !name) {
          setError('Incomplete authentication data received.');
          setLoading(false);
          return;
        }

        // Create user object from query parameters
        const user = {
          id: userId,
          username: username || email,
          email: email,
          name: name
        };

        // Set authentication data
        await setAuthFromCallback(token, user);

        // Redirect to dashboard
        navigate('/dashboard');
      } catch (error: any) {
        console.error('Google OAuth callback error:', error);
        setError(error.message || 'Authentication failed');
        setLoading(false);
      }
    };

    handleCallback();
  }, [searchParams, navigate, setAuthFromCallback]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500 mx-auto mb-4"></div>
          <p className="text-white text-lg">Completing Google authentication...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-900/20 border border-red-800/30 rounded-lg p-4 mb-4">
            <p className="text-red-300">{error}</p>
          </div>
          <button
            onClick={() => navigate('/login')}
            className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
          >
            Back to Login
          </button>
        </div>
      </div>
    );
  }

  return null;
};

export default GoogleCallback;
