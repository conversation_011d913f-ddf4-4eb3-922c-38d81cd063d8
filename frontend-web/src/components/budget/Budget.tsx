import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Target, TrendingUp, TrendingDown, X } from 'lucide-react';
import { categoriesAPI, budgetAPI } from '../../services/api';

interface BudgetItem {
  id: string;
  category_id: string;
  category_name: string;
  amount: number;
  spent: number;
  period: 'monthly' | 'yearly';
  percentage: number;
  status: 'good' | 'warning' | 'danger';
}

const Budget: React.FC = () => {
  const [budgets, setBudgets] = useState<BudgetItem[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingBudget, setEditingBudget] = useState<BudgetItem | null>(null);
  const [formData, setFormData] = useState({
    category_id: '',
    amount: '',
    period: 'monthly'
  });

  // Fetch all data
  const loadData = async () => {
    try {
      setLoading(true);

      const [categoriesRes, budgetsRes] = await Promise.all([
        categoriesAPI.getAll(),
        budgetAPI.getAll()
      ]);

      const categoriesData = categoriesRes.data || [];
      const budgetsData = budgetsRes.data || [];

      setCategories(categoriesData);

      // Process budgets with spending data
      const processedBudgets = budgetsData.map((budget: any) => {
        const category = categoriesData.find((c: any) => c.id === budget.category_id);
        const categoryName = category?.name || 'Unknown';

        // Use spent value from backend
        const spent = budget.spent || 0;

        const percentage = budget.amount > 0 ? (spent / budget.amount) * 100 : 0;
        let status: 'good' | 'warning' | 'danger' = 'good';

        if (percentage >= 100) status = 'danger';
        else if (percentage >= 80) status = 'warning';

        return {
          id: budget.id,
          category_id: budget.category_id,
          category_name: categoryName,
          amount: budget.amount,
          spent,
          period: budget.period,
          percentage,
          status
        };
      });

      setBudgets(processedBudgets);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const budgetData = {
        amount: parseFloat(formData.amount),
        period: formData.period as 'monthly' | 'yearly',
        category_id: formData.category_id
      };

      if (editingBudget) {
        await budgetAPI.update(editingBudget.id, budgetData);
      } else {
        await budgetAPI.create(budgetData);
      }

      await loadData();
      closeModal();
    } catch (error) {
      console.error('Error saving budget:', error);
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (window.confirm('Bu bütçeyi silmek istediğinizden emin misiniz?')) {
      try {
        await budgetAPI.delete(id);
        await loadData();
      } catch (error) {
        console.error('Error deleting budget:', error);
      }
    }
  };

  // Handle edit
  const handleEdit = (budget: BudgetItem) => {
    setEditingBudget(budget);
    setFormData({
      category_id: budget.category_id,
      amount: budget.amount.toString(),
      period: budget.period
    });
    setShowModal(true);
  };

  // Close modal
  const closeModal = () => {
    setShowModal(false);
    setEditingBudget(null);
    setFormData({ category_id: '', amount: '', period: 'monthly' });
  };

  // Calculate totals
  const totalBudget = budgets.reduce((sum, b) => sum + b.amount, 0);
  const totalSpent = budgets.reduce((sum, b) => sum + b.spent, 0);
  const remaining = totalBudget - totalSpent;

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-700 rounded w-48 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-slate-800 rounded-xl"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-slate-800 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Bütçe Yönetimi</h1>
          <p className="text-slate-400 mt-1">Harcama limitlerini takip edin</p>
        </div>
        <button
          onClick={() => setShowModal(true)}
          className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Bütçe Ekle
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-slate-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Toplam Bütçe</p>
              <p className="text-2xl font-bold text-white">₺{totalBudget.toFixed(2)}</p>
            </div>
            <Target className="h-8 w-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-slate-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Toplam Harcama</p>
              <p className="text-2xl font-bold text-red-400">₺{totalSpent.toFixed(2)}</p>
            </div>
            <TrendingDown className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-slate-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Kalan Bütçe</p>
              <p className={`text-2xl font-bold ${remaining >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ₺{remaining.toFixed(2)}
              </p>
            </div>
            <TrendingUp className={`h-8 w-8 ${remaining >= 0 ? 'text-green-400' : 'text-red-400'}`} />
          </div>
        </div>
      </div>

      {/* Budget List */}
      <div className="bg-slate-800 rounded-xl overflow-hidden">
        <div className="px-6 py-4 border-b border-slate-700">
          <h3 className="text-xl font-semibold text-white">Kategori Bütçeleri</h3>
        </div>

        {budgets.length === 0 ? (
          <div className="p-12 text-center">
            <Target className="h-12 w-12 text-slate-500 mx-auto mb-4" />
            <p className="text-slate-400">Henüz bütçe belirlenmemiş</p>
            <p className="text-slate-500 text-sm mt-1">İlk bütçenizi oluşturun</p>
          </div>
        ) : (
          <div className="divide-y divide-slate-700">
            {budgets.map((budget) => (
              <div key={budget.id} className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-white font-medium">{budget.category_name}</h4>
                    <p className="text-slate-400 text-sm">
                      {budget.period === 'monthly' ? 'Aylık' : 'Yıllık'} bütçe
                    </p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-white font-semibold">
                        ₺{budget.spent.toFixed(2)} / ₺{budget.amount.toFixed(2)}
                      </p>
                      <p className={`text-sm ${
                        budget.status === 'danger' ? 'text-red-400' :
                        budget.status === 'warning' ? 'text-yellow-400' : 'text-green-400'
                      }`}>
                        {budget.percentage.toFixed(1)}% kullanıldı
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(budget)}
                        className="p-2 text-indigo-400 hover:text-indigo-300 hover:bg-indigo-500/20 rounded-lg transition-colors"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(budget.id)}
                        className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="w-full bg-slate-700 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      budget.status === 'danger' ? 'bg-red-500' :
                      budget.status === 'warning' ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.min(budget.percentage, 100)}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-xl w-full max-w-md">
            <div className="flex items-center justify-between p-6 border-b border-slate-700">
              <h3 className="text-xl font-semibold text-white">
                {editingBudget ? 'Bütçe Düzenle' : 'Bütçe Ekle'}
              </h3>
              <button
                onClick={closeModal}
                className="text-slate-400 hover:text-white transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Kategori
                </label>
                <select
                  name="category_id"
                  value={formData.category_id}
                  onChange={(e) => setFormData({...formData, category_id: e.target.value})}
                  required
                  disabled={!!editingBudget}
                  className="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  <option value="">Kategori seçin</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Bütçe Tutarı
                </label>
                <input
                  type="number"
                  name="amount"
                  value={formData.amount}
                  onChange={(e) => setFormData({...formData, amount: e.target.value})}
                  required
                  step="0.01"
                  min="0"
                  className="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Dönem
                </label>
                <select
                  name="period"
                  value={formData.period}
                  onChange={(e) => setFormData({...formData, period: e.target.value})}
                  className="w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="monthly">Aylık</option>
                  <option value="yearly">Yıllık</option>
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={closeModal}
                  className="flex-1 px-4 py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  {editingBudget ? 'Güncelle' : 'Ekle'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Budget;