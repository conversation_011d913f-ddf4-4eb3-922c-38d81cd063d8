import React from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  CreditCard,
  FileText,
  TrendingUp,
  Target,
  LogOut,
  Menu,
  X,
  User,
  Tag
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import logoImage from '../../assets/logo-butce360.png';
import LanguageSwitcher from '../common/LanguageSwitcher';

// Butce360 Logo Component
const Butce360Logo: React.FC<{ className?: string; size?: 'sm' | 'md' | 'lg' }> = ({
  className = "",
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'h-12 w-full max-w-none',
    md: 'h-16 w-full max-w-none',
    lg: 'h-20 w-full max-w-none'
  };

  return (
    <div className={`flex items-center justify-center w-full h-full p-2 ${className}`}>
      <img
        src={logoImage}
        alt="Bütçe360 Logo"
        className={`${sizeClasses[size]} object-contain`}
        style={{ maxWidth: 'none', width: '100%' }}
      />
    </div>
  );
};

const Layout: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  const navigation = [
    { name: t('navigation.dashboard'), href: '/dashboard', icon: LayoutDashboard },
    { name: t('navigation.transactions'), href: '/transactions', icon: CreditCard },
    { name: t('navigation.categories'), href: '/categories', icon: Tag },
    { name: t('navigation.reports'), href: '/reports', icon: TrendingUp },
    { name: t('navigation.budget'), href: '/budget', icon: Target },
    { name: t('navigation.bankStatements'), href: '/bank-statements', icon: FileText },
  ];

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="flex h-screen bg-slate-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 rounded-full blur-3xl pointer-events-none"></div>
      <div className="absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-br from-pink-500/5 to-orange-500/5 rounded-full blur-3xl pointer-events-none"></div>
      
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
        <div className="fixed left-0 top-0 h-full w-64 bg-slate-800 shadow-2xl border-r border-slate-700/50">
          <div className="flex items-center justify-between p-4 h-20 border-b border-slate-700/50">
            <div className="flex-1">
              <Butce360Logo size="sm" />
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-slate-400 hover:text-white transition-colors p-1"
            >
              <X size={20} />
            </button>
          </div>
          <nav className="mt-6 px-3">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={`flex items-center px-4 py-3 mb-2 text-sm font-medium transition-all duration-200 rounded-lg ${
                    isActive
                      ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                      : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'
                  }`}
                >
                  <Icon size={18} className="mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>
          <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-slate-700/50">
            {user && (
              <div className="flex items-center px-4 py-3 mb-3 text-sm text-slate-300 bg-slate-700/30 rounded-lg">
                <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                  <User size={14} className="text-white" />
                </div>
                <span className="truncate font-medium">{user.name}</span>
              </div>
            )}
            <div className="mb-3">
              <LanguageSwitcher />
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-4 py-3 text-sm font-medium text-slate-300 hover:bg-slate-700/50 hover:text-white rounded-lg transition-all duration-200"
            >
              <LogOut size={18} className="mr-3" />
              {t('auth.logout')}
            </button>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:flex lg:flex-col lg:w-64 lg:bg-slate-800 lg:shadow-2xl lg:border-r lg:border-slate-700/50">
        <div className="flex items-center justify-center p-6 h-20 border-b border-slate-700/50">
          <Butce360Logo size="md" />
        </div>
        <nav className="flex-1 mt-6 px-3">
          {navigation.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.href;
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center px-4 py-3 mb-2 text-sm font-medium transition-all duration-200 rounded-lg ${
                  isActive
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                    : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'
                }`}
              >
                <Icon size={18} className="mr-3" />
                {item.name}
              </Link>
            );
          })}
        </nav>
        <div className="p-4 border-t border-slate-700/50">
          {user && (
            <div className="flex items-center px-4 py-3 mb-3 text-sm text-slate-300 bg-slate-700/30 rounded-lg">
              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                <User size={14} className="text-white" />
              </div>
              <span className="truncate font-medium">{user.name}</span>
            </div>
          )}
          <div className="mb-3">
            <LanguageSwitcher />
          </div>
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-4 py-3 text-sm font-medium text-slate-300 hover:bg-slate-700/50 hover:text-white rounded-lg transition-all duration-200"
          >
            <LogOut size={18} className="mr-3" />
            {t('auth.logout')}
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden relative">
        {/* Mobile header */}
        <div className="lg:hidden bg-slate-800 shadow-lg border-b border-slate-700/50">
          <div className="flex items-center justify-between p-4 h-16">
            <button
              onClick={() => setSidebarOpen(true)}
              className="text-slate-400 hover:text-white transition-colors p-1"
            >
              <Menu size={20} />
            </button>
            <div className="flex-1 flex justify-center mx-4">
              <Butce360Logo size="sm" />
            </div>
            <div className="w-6" />
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 overflow-y-auto bg-slate-900 p-6 relative">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default Layout;
