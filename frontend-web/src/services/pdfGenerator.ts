import jsPDF from 'jspdf';

interface BankStatementData {
  bankName: string;
  accountNumber: string;
  accountHolder: string;
  statementPeriod: string;
  transactions: Array<{
    date: string;
    description: string;
    amount: number;
    type: 'income' | 'expense';
    installment?: string;
  }>;
}

export class PDFGenerator {
  private static addHeader(doc: jsPDF, bankName: string, accountNumber: string, accountHolder: string, period: string) {
    // Bank logo area (placeholder)
    doc.setFillColor(0, 71, 196); // #0474C4
    doc.rect(20, 20, 40, 20, 'F');
    
    // Bank name
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 71, 196);
    doc.text(bankName, 70, 30);
    
    // Statement title
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.text('HESAP EKSTRESİ', 70, 40);
    
    // Account info
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`Hesap No: ${accountNumber}`, 20, 60);
    doc.text(`Hesap Sahibi: ${accountHolder}`, 20, 70);
    doc.text(`Dönem: ${period}`, 20, 80);
    
    // Line separator
    doc.setDrawColor(200, 200, 200);
    doc.line(20, 90, 190, 90);
  }

  private static addTransactionTable(doc: jsPDF, transactions: BankStatementData['transactions']) {
    let yPosition = 110;
    
    // Table headers
    doc.setFontSize(9);
    doc.setFont('helvetica', 'bold');
    doc.setFillColor(240, 240, 240);
    doc.rect(20, yPosition - 5, 170, 10, 'F');
    
    doc.text('İşlem Tarihi', 25, yPosition);
    doc.text('Açıklama', 60, yPosition);
    doc.text('Taksit', 130, yPosition);
    doc.text('Tutar', 160, yPosition);
    
    yPosition += 15;
    
    // Transaction rows
    doc.setFont('helvetica', 'normal');
    transactions.forEach((transaction, index) => {
      if (yPosition > 270) {
        doc.addPage();
        yPosition = 30;
      }
      
      // Alternate row colors
      if (index % 2 === 0) {
        doc.setFillColor(250, 250, 250);
        doc.rect(20, yPosition - 5, 170, 10, 'F');
      }
      
      doc.setTextColor(0, 0, 0);
      doc.text(transaction.date, 25, yPosition);
      
      // Description (truncate if too long)
      const description = transaction.description.length > 35 
        ? transaction.description.substring(0, 35) + '...' 
        : transaction.description;
      doc.text(description, 60, yPosition);
      
      // Installment
      doc.text(transaction.installment || '-', 130, yPosition);
      
      // Amount with color coding
      const amountText = `${transaction.amount.toFixed(2)} TL`;
      if (transaction.type === 'income') {
        doc.setTextColor(0, 150, 0); // Green for income
        doc.text(`+${amountText}`, 160, yPosition);
      } else {
        doc.setTextColor(200, 0, 0); // Red for expense
        doc.text(`-${amountText}`, 160, yPosition);
      }
      
      yPosition += 12;
    });
  }

  private static addFooter(doc: jsPDF) {
    const pageHeight = doc.internal.pageSize.height;
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text('Bu belge örnek amaçlı oluşturulmuştur.', 20, pageHeight - 20);
    doc.text(`Oluşturulma Tarihi: ${new Date().toLocaleDateString('tr-TR')}`, 20, pageHeight - 10);
  }

  static generateVakifbankStatement(): void {
    const doc = new jsPDF();
    
    const data: BankStatementData = {
      bankName: 'VakıfBank',
      accountNumber: '****************',
      accountHolder: 'ÖRNEK KULLANICI',
      statementPeriod: '01.01.2024 - 31.01.2024',
      transactions: [
        { date: '01.01.2024', description: 'Maaş Ödemesi', amount: 15000, type: 'income' },
        { date: '02.01.2024', description: 'Market Alışverişi - MIGROS', amount: 250.50, type: 'expense' },
        { date: '03.01.2024', description: 'Elektrik Faturası', amount: 180.75, type: 'expense' },
        { date: '05.01.2024', description: 'ATM Para Çekme', amount: 500, type: 'expense' },
        { date: '08.01.2024', description: 'Online Alışveriş - TRENDYOL', amount: 125.90, type: 'expense' },
        { date: '10.01.2024', description: 'Kira Ödemesi', amount: 3500, type: 'expense' },
        { date: '12.01.2024', description: 'Freelance Gelir', amount: 2500, type: 'income' },
        { date: '15.01.2024', description: 'Benzin - SHELL', amount: 300, type: 'expense' },
        { date: '18.01.2024', description: 'Restoran - BURGER KING', amount: 85.50, type: 'expense' },
        { date: '20.01.2024', description: 'Su Faturası', amount: 45.25, type: 'expense' },
        { date: '22.01.2024', description: 'Eczane Alışverişi', amount: 120.80, type: 'expense' },
        { date: '25.01.2024', description: 'Bonus Ödemesi', amount: 1000, type: 'income' },
        { date: '28.01.2024', description: 'İnternet Faturası', amount: 89.90, type: 'expense' },
        { date: '30.01.2024', description: 'Kitap Alışverişi - D&R', amount: 75.50, type: 'expense' }
      ]
    };
    
    this.addHeader(doc, data.bankName, data.accountNumber, data.accountHolder, data.statementPeriod);
    this.addTransactionTable(doc, data.transactions);
    this.addFooter(doc);
    
    doc.save('vakifbank-ornek-ekstre.pdf');
  }

  static generateEnparaStatement(): void {
    const doc = new jsPDF();
    
    const data: BankStatementData = {
      bankName: 'Enpara.com',
      accountNumber: '****************',
      accountHolder: 'ÖRNEK KULLANICI',
      statementPeriod: '01.02.2024 - 29.02.2024',
      transactions: [
        { date: '01/02/2024', description: 'Maaş Yatırımı', amount: 16500, type: 'income' },
        { date: '02/02/2024', description: 'CARREFOUR MARKET', amount: 320.75, type: 'expense', installment: '1/1' },
        { date: '03/02/2024', description: 'Doğalgaz Faturası', amount: 280.50, type: 'expense' },
        { date: '05/02/2024', description: 'POS İşlemi - STARBUCKS', amount: 45.90, type: 'expense' },
        { date: '07/02/2024', description: 'EFT Gönderimi', amount: 1000, type: 'expense' },
        { date: '10/02/2024', description: 'Kira Tahsilatı', amount: 4000, type: 'expense' },
        { date: '12/02/2024', description: 'Kredi Kartı Ödemesi', amount: 2500, type: 'expense', installment: '3/12' },
        { date: '14/02/2024', description: 'AMAZON PRIME', amount: 29.90, type: 'expense' },
        { date: '16/02/2024', description: 'Havale Geliri', amount: 800, type: 'income' },
        { date: '18/02/2024', description: 'OPET Benzin', amount: 350, type: 'expense' },
        { date: '20/02/2024', description: 'NETFLIX Abonelik', amount: 63.99, type: 'expense' },
        { date: '22/02/2024', description: 'Ek Gelir', amount: 1200, type: 'income' },
        { date: '25/02/2024', description: 'Telefon Faturası', amount: 125.50, type: 'expense' },
        { date: '28/02/2024', description: 'GETIR Sipariş', amount: 95.25, type: 'expense' }
      ]
    };
    
    this.addHeader(doc, data.bankName, data.accountNumber, data.accountHolder, data.statementPeriod);
    this.addTransactionTable(doc, data.transactions);
    this.addFooter(doc);
    
    doc.save('enpara-ornek-ekstre.pdf');
  }

  static generateGarantiStatement(): void {
    const doc = new jsPDF();
    
    const data: BankStatementData = {
      bankName: 'Garanti BBVA',
      accountNumber: '****************',
      accountHolder: 'ÖRNEK KULLANICI',
      statementPeriod: '01.03.2024 - 31.03.2024',
      transactions: [
        { date: '01.03.2024', description: 'Maaş Transferi', amount: 17200, type: 'income' },
        { date: '02.03.2024', description: 'BİM MARKET', amount: 180.25, type: 'expense' },
        { date: '04.03.2024', description: 'Elektrik Faturası - BEDAŞ', amount: 195.80, type: 'expense' },
        { date: '06.03.2024', description: 'ATM Nakit Çekim', amount: 800, type: 'expense' },
        { date: '08.03.2024', description: 'HEPSIBURADA Alışveriş', amount: 450.90, type: 'expense' },
        { date: '10.03.2024', description: 'Kira Gideri', amount: 3800, type: 'expense' },
        { date: '12.03.2024', description: 'Danışmanlık Ücreti', amount: 3000, type: 'income' },
        { date: '15.03.2024', description: 'BP Petrol', amount: 280, type: 'expense' },
        { date: '17.03.2024', description: 'DOMINOS PIZZA', amount: 120.50, type: 'expense' },
        { date: '19.03.2024', description: 'İSKİ Su Faturası', amount: 52.75, type: 'expense' },
        { date: '21.03.2024', description: 'Eczane - BENU', amount: 85.40, type: 'expense' },
        { date: '23.03.2024', description: 'Proje Ödemesi', amount: 2200, type: 'income' },
        { date: '26.03.2024', description: 'Turkcell Fatura', amount: 149.90, type: 'expense' },
        { date: '29.03.2024', description: 'TEKNOSA Elektronik', amount: 1250, type: 'expense' }
      ]
    };
    
    this.addHeader(doc, data.bankName, data.accountNumber, data.accountHolder, data.statementPeriod);
    this.addTransactionTable(doc, data.transactions);
    this.addFooter(doc);
    
    doc.save('garanti-ornek-ekstre.pdf');
  }
}
