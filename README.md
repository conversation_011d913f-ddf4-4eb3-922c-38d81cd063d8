# Gelir & Gider Takip Uygulaması (Income & Expense Tracking Application)

A comprehensive financial tracking application with RESTful API endpoints for authentication, users, transactions, categories, accounts, payment methods, and reporting.

## Features

- User authentication with JWT tokens
- Transaction management (income and expenses)
- Category management
- Account management
- Reporting (summary, category breakdown, monthly reports)
- Location-based expense tracking
- Recurring transactions

## API Documentation

The API documentation is available through Swagger UI:

- `/swagger/index.html` - Swagger UI without authentication
- `/docs` - Redirects to Swagger UI

## Getting Started

### Prerequisites

- Go 1.16 or higher
- PostgreSQL database

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/NocyTech/fin_notebook.git
   cd fin_notebook
   ```

2. Install dependencies:
   ```
   go mod download
   ```

3. Configure the application by setting environment variables or updating the config file.

4. Run the application:
   ```
   go run main.go
   ```

### Seeding Data

The application includes functionality to seed the database with initial data for testing and development purposes. There are two ways to seed the database:

#### Option 1: Using Command-Line Flag

Run the application with the `--seed` flag:
```
go run main.go --seed
```

This will seed the database and exit without starting the server.

#### Option 2: Using Environment Variable

Set the `SEED_DATA` environment variable to `true` before starting the application:
```
SEED_DATA=true go run main.go
```

This will seed the database and then start the server.

### Seed Data Contents

The seed data includes:

- Demo user (username: `demo`, password: `demo123`)
- Default expense categories (Groceries, Dining Out, Transportation, etc.)
- Default income categories (Salary, Freelance, Investments, etc.)
- Default accounts (Cash, Bank Account, Credit Card)
- Sample transactions for the past 3 months
- Sample recurring transactions

## API Endpoints

### Authentication

- `POST /register` - Register a new user
- `POST /login` - Login and get JWT token
- `GET /me` - Get current user information

### Transactions

- `POST /transactions` - Create a new transaction
- `GET /transactions` - Get all transactions with optional filtering
- `GET /transactions/{id}` - Get transaction by ID
- `PUT /transactions/{id}` - Update transaction
- `DELETE /transactions/{id}` - Delete transaction

### Categories

- `POST /categories` - Create a new category
- `GET /categories` - Get all categories with optional filtering by type
- `GET /categories/{id}` - Get category by ID
- `PUT /categories/{id}` - Update category
- `DELETE /categories/{id}` - Delete category

### Accounts

- `POST /accounts` - Create a new account
- `GET /accounts` - Get all accounts
- `GET /accounts/{id}` - Get account by ID
- `PUT /accounts/{id}` - Update account
- `DELETE /accounts/{id}` - Delete account

### Reports

- `GET /reports/summary` - Get financial summary
- `GET /reports/category-breakdown` - Get category breakdown
- `GET /reports/monthly` - Get monthly report
- `GET /reports/location-summary` - Get location summary

### Recurring Transactions

- `POST /recurring-transactions` - Create a new recurring transaction
- `GET /recurring-transactions` - Get all recurring transactions
- `GET /recurring-transactions/{id}` - Get recurring transaction by ID
- `PUT /recurring-transactions/{id}` - Update recurring transaction
- `DELETE /recurring-transactions/{id}` - Delete recurring transaction

## License

This project is licensed under the MIT License - see the LICENSE file for details.
